name: Changed lock files
on:
  pull_request_target: 
    types: [opened, reopened]

permissions:
  pull-requests: read

jobs:
  lockfiles:
    runs-on: ubuntu-latest
    name: Verify lock file integrity
    steps:
      - name: Clone Tabler
        uses: actions/checkout@v4

      - name: Prevent lock file change
        uses: xalvarez/prevent-file-change-action@v2
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          pattern: Gemfile.lock|pnpm-lock.json
          trustedAuthors: <AUTHORS>
