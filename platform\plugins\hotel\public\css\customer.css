/*!
 * Cropper v0.7.1
 * https://github.com/fengyuanchen/cropper
 *
 * Copyright 2014 <PERSON><PERSON>
 * Released under the MIT license
 */
.cropper-container {
  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
.cropper-container img {
  width: 100%;
  max-width: none !important;
  height: 100%;
  max-height: none !important;
}

.cropper-modal,
.cropper-canvas {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cropper-canvas {
  background-color: #ffffff;
  filter: alpha(opacity=0);
  opacity: 0;
}

.cropper-modal {
  background-color: #000000;
  filter: alpha(opacity=50);
  opacity: 0.5;
}

.cropper-dragger {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
}

.cropper-viewer {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline-width: 1px;
  outline-style: solid;
  outline-color: #6699ff;
  outline-color: rgba(51, 102, 255, 0.75);
}

.cropper-dashed {
  position: absolute;
  display: block;
  filter: alpha(opacity=50);
  border: 0 dashed #ffffff;
  opacity: 0.5;
}
.cropper-dashed.dashed-h {
  top: 33.3%;
  left: 0;
  width: 100%;
  height: 33.3%;
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.cropper-dashed.dashed-v {
  top: 0;
  left: 33.3%;
  width: 33.3%;
  height: 100%;
  border-right-width: 1px;
  border-left-width: 1px;
}

.cropper-face,
.cropper-line,
.cropper-point {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=10);
  opacity: 0.1;
}

.cropper-face {
  top: 0;
  left: 0;
  cursor: move;
  background-color: #ffffff;
}

.cropper-line {
  background-color: #6699ff;
}
.cropper-line.line-e {
  top: 0;
  right: -3px;
  width: 5px;
  cursor: e-resize;
}
.cropper-line.line-n {
  top: -3px;
  left: 0;
  height: 5px;
  cursor: n-resize;
}
.cropper-line.line-w {
  top: 0;
  left: -3px;
  width: 5px;
  cursor: w-resize;
}
.cropper-line.line-s {
  bottom: -3px;
  left: 0;
  height: 5px;
  cursor: s-resize;
}

.cropper-point {
  width: 5px;
  height: 5px;
  background-color: #6699ff;
  filter: alpha(opacity=75);
  opacity: 0.75;
}
.cropper-point.point-e {
  top: 50%;
  right: -3px;
  margin-top: -3px;
  cursor: e-resize;
}
.cropper-point.point-n {
  top: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}
.cropper-point.point-w {
  top: 50%;
  left: -3px;
  margin-top: -3px;
  cursor: w-resize;
}
.cropper-point.point-s {
  bottom: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: s-resize;
}
.cropper-point.point-ne {
  top: -3px;
  right: -3px;
  cursor: ne-resize;
}
.cropper-point.point-nw {
  top: -3px;
  left: -3px;
  cursor: nw-resize;
}
.cropper-point.point-sw {
  bottom: -3px;
  left: -3px;
  cursor: sw-resize;
}
.cropper-point.point-se {
  right: -3px;
  bottom: -3px;
  width: 20px;
  height: 20px;
  cursor: se-resize;
  filter: alpha(opacity=100);
  opacity: 1;
}
.cropper-point.point-se:before {
  position: absolute;
  right: -50%;
  bottom: -50%;
  display: block;
  width: 200%;
  height: 200%;
  content: " ";
  background-color: #6699ff;
  filter: alpha(opacity=0);
  opacity: 0;
}

@media (min-width: 768px) {
  .cropper-point.point-se {
    width: 15px;
    height: 15px;
  }
}
@media (min-width: 992px) {
  .cropper-point.point-se {
    width: 10px;
    height: 10px;
  }
}
@media (min-width: 1200px) {
  .cropper-point.point-se {
    width: 5px;
    height: 5px;
    filter: alpha(opacity=75);
    opacity: 0.75;
  }
}
.cropper-hidden {
  display: none !important;
}

.cropper-invisible {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: auto !important;
  max-width: none !important;
  height: auto !important;
  max-height: none !important;
  filter: alpha(opacity=0);
  opacity: 0;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

.cropper-disabled .cropper-canvas,
.cropper-disabled .cropper-face,
.cropper-disabled .cropper-line,
.cropper-disabled .cropper-point {
  cursor: not-allowed;
}

.avatar-body {
  padding-right: 15px;
  padding-left: 15px;
}

.avatar-upload {
  overflow: hidden;
}
.avatar-upload label {
  display: block;
  float: left;
  clear: left;
  width: 100px;
}
.avatar-upload input {
  display: block;
  margin-left: 110px;
}

.avater-alert {
  margin-top: 10px;
  margin-bottom: 10px;
}

.avatar-wrapper {
  height: 364px;
  width: 100%;
  margin-top: 15px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.25);
  background-color: #fcfcfc;
  overflow: hidden;
}
.avatar-wrapper img {
  display: block;
  height: auto;
  max-width: 100%;
}

.avatar-preview {
  float: left;
  margin-top: 15px;
  margin-right: 15px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  background-color: #ffffff;
  overflow: hidden;
}
.avatar-preview:hover {
  border-color: #ccccff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
}
.avatar-preview img {
  width: 100%;
}

.preview-lg {
  height: 184px;
  width: 184px;
  margin-top: 15px;
}

.preview-md {
  height: 100px;
  width: 100px;
}

.preview-sm {
  height: 50px;
  width: 50px;
}

@media (min-width: 992px) {
  .avatar-preview {
    float: none;
  }
}
.cropper-loading {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #ffffff url("/vendor/core/images/loading.gif") no-repeat center center;
  opacity: 0.75;
  filter: alpha(opacity=75);
  z-index: 20140628;
}

.avatar-view {
  cursor: pointer;
}

.customer-page {
  margin: 0;
  background-color: #ffffff;
}
.customer-page .customer-body {
  padding: 50px 20px;
}
.customer-page .border-border {
  border: 1px solid #cecece;
}
.customer-page .userpic-avatar {
  border: 2px solid #ffffff;
  border-radius: 50%;
  width: 70%;
  margin: 0 auto;
}
.customer-page .profile-sidebar {
  padding: 20px;
  border-right: 1px solid #e2e2e2;
  color: rgb(228, 228, 228);
  height: 100%;
  background-color: #ffffff;
  box-shadow: 5px 5px 5px;
  -moz-box-shadow: 5px 5px 5px;
  -webkit-box-shadow: 5px 5px 5px;
  -o-box-shadow: 5px 5px 5px;
}
.customer-page .profile-sidebar .profile-usermenu {
  margin: 20px 0;
  list-style: none;
  display: block;
}
.customer-page .profile-sidebar .profile-usermenu li.list-group-item {
  display: block;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-bottom: 1px dashed #cecece;
  margin-bottom: 1px;
}
.customer-page .profile-sidebar .profile-usermenu li.list-group-item i {
  float: right;
  color: rgb(82, 82, 82);
  line-height: 25px;
  font-size: 1.1em;
}
.customer-page .profile-sidebar .profile-usermenu li.list-group-item a {
  color: rgb(0, 0, 0);
  padding-right: 30px;
}
.customer-page .profile-sidebar .profile-usermenu li.list-group-item a:active {
  text-decoration: none;
  color: rgb(0, 138, 143);
  font-weight: bold;
}
.customer-page .profile-sidebar .profile-usertitle-name {
  font-size: 1em;
  margin: 20px 0;
  text-align: center;
  color: rgb(0, 131, 136);
  line-break: anywhere;
}
.customer-page .profile-content {
  padding: 20px 20px 20px 50px;
}
.customer-page h2.customer-page-title {
  text-align: center;
  font-size: 1.4em;
  font-weight: bold;
}
.customer-page .customer-list-order {
  margin-top: 40px;
}
.customer-page .customer-list-order a.btn-order-detail {
  text-transform: capitalize;
  border: none;
  background-color: #e69f00;
  padding: 5px;
  color: #ececec;
  border-radius: 2px;
}
.customer-page .customer-order-detail {
  margin-top: 50px;
}
.customer-page .customer-order-detail .btn-print {
  border-radius: 3px;
  border: 1px solid rgb(0, 0, 0);
  border-left: none;
  height: 40px;
  background-color: rgb(177, 177, 177);
  padding: 3px 10px;
  line-height: 25px;
  color: #000000;
}
.customer-page .customer-order-detail .order-slogan {
  text-align: left;
}
.customer-page .customer-order-detail p {
  margin: 0;
}
.customer-page .customer-order-detail .order-meta {
  text-align: right;
}
.customer-page .customer-order-detail span {
  min-width: 150px;
  display: inline-block;
  margin: 5px 0;
}
.customer-page .customer-order-detail span.order-detail-value {
  padding: 5px;
  line-height: 10px;
  border-bottom: 1px dashed #cecece;
  margin-left: 20px;
  color: #000000;
  font-weight: bold;
  display: inline-block;
}
.customer-page .customer-order-detail span h5 {
  text-align: center;
  margin: 30px 0;
  width: 100%;
}
.customer-page .add-address {
  border: 1px dashed #d8d8d8;
  -webkit-border-radius: 4px;
  display: block;
  padding: 20px;
  background: #ffffff;
  text-align: center;
  margin-bottom: 10px;
  font-size: 15px;
  position: relative;
}
.customer-page .dashboard-address {
  margin-bottom: 20px;
}
.customer-page .dashboard-address .dashboard-address-item {
  position: relative;
  margin-bottom: 0;
  margin-top: 10px;
  box-shadow: none;
  border-radius: 4px;
  border: 1px solid #cccccc;
}
.customer-page .dashboard-address .panel-body {
  padding: 17px;
}
.customer-page .dashboard-address .dashboard-address-item .name {
  font-size: 13px;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-weight: bold;
}
.customer-page .dashboard-address .dashboard-address-item.is-address-default .address-default {
  display: inline-block;
  font-size: 12px;
  color: #26bc4e;
  text-transform: none;
  font-weight: 400;
  position: relative;
  padding-left: 25px;
}
.customer-page .dashboard-address .dashboard-address-item .address {
  font-size: 13px;
  margin-bottom: 5px;
  color: #242424;
  margin-right: 20px;
}
.customer-page .dashboard-address .dashboard-address-item .phone {
  font-size: 13px;
  margin-bottom: 0;
  color: #242424;
  margin-right: 20px;
}
.customer-page .dashboard-address .dashboard-address-item .action {
  margin-bottom: 0;
  font-size: 0;
}
.customer-page .dashboard-address .edit-customer-address {
  position: absolute;
  border: none;
  right: 45px;
  top: 10px;
  color: #007ff0;
  font-size: 14px;
  background: 0 0;
}
.customer-page .dashboard-address .customer-checkbox {
  border-radius: 3px;
  height: 20px;
  padding: 5px;
  -webkit-appearance: normal !important;
  -moz-appearance: normal !important;
}

.profile-image {
  cursor: pointer;
}

.mt-card-avatar-circle {
  border-radius: 50%;
}

.mt-card-avatar {
  -webkit-mask-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYGBgAAgwAAAEAAGbA+oJAAAAAElFTkSuQmCC);
  overflow: hidden;
  position: relative;
  text-align: center;
}

.avatar-view {
  cursor: pointer;
}
.avatar-view img {
  border-radius: 50%;
}

.mt-card-avatar-circle .mt-overlay {
  border-radius: 50%;
}

.mt-card-avatar .mt-overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.7);
  transition: all 0.4s ease-in-out;
  cursor: pointer;
  border-radius: 50%;
}

.mt-card-avatar .mt-overlay > span {
  color: #fff;
  position: absolute;
  top: 40%;
  font-size: 20px;
}

.mt-card-avatar:hover .mt-overlay {
  opacity: 1;
  filter: alpha(opacity=100);
  transform: translateZ(0);
}

.avatar-preview {
  margin-top: 15px;
  margin-right: 15px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  overflow: hidden;
  border-radius: 4px;
}

.preview-lg {
  width: 185px;
  height: 185px;
  margin-top: 15px;
}

.avatar-preview-wrapper img {
  max-width: 100%;
  margin-bottom: 15px;
}

.avatar-wrapper {
  height: 364px;
  width: 100%;
  margin-top: 15px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.25);
  background-color: #fcfcfc;
  overflow: hidden;
}

.avatar-upload {
  text-align: left;
  overflow: hidden;
}

.avatar-body {
  padding-right: 15px;
  padding-left: 15px;
}

.modal-body {
  position: relative;
}

.crop-avatar .modal-title {
  text-align: left;
}

.show-admin-bar .modal {
  top: 80px;
}
