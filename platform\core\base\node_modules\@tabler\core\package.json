{"private": true, "description": "Premium and Open Source dashboard template with responsive and high quality UI.", "homepage": "https://tabler.io", "scripts": {"build": "turbo build && pnpm run zip-package", "dev": "turbo dev", "clean": "turbo clean", "bundlewatch": "turbo bundlewatch", "version": "changeset version", "publish": "changeset publish", "playwright": "pnpm run build && pnpm run vt", "reformat-md": "node .build/reformat-md.mjs", "zip-package": "node .build/zip-package.mjs", "start": "pnpm dev"}, "packageManager": "pnpm@10.11.0", "dependencies": {"shx": "^0.4.0"}, "devDependencies": {"@argos-ci/playwright": "^5.0.3", "@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@playwright/test": "^1.52.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "adm-zip": "^0.5.16", "autoprefixer": "^10.4.21", "bundlewatch": "^0.4.1", "clean-css-cli": "^5.6.3", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "fs-extra": "^11.3.0", "glob": "^11.0.2", "js-beautify": "^1.15.4", "nodemon": "^3.1.10", "pnpm": "10.6.5", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "prettier": "^3.5.3", "rollup": "4.40.2", "rollup-plugin-dotenv": "^0.5.1", "rtlcss": "^4.3.0", "sass": "1.71.0", "shelljs": "^0.10.0", "terser": "^5.39.2", "turbo": "^2.5.3"}}