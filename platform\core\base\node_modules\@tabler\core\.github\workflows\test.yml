name: Test build

on:
  pull_request: null

env:
  NODE: 20

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with: 
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "${{ env.NODE }}"

      - name: Install PNPM
        uses: pnpm/action-setup@v4

      - run: node --version

      - name: Install pnpm dependencies
        run: pnpm install

      - name: Build
        run: pnpm run build
