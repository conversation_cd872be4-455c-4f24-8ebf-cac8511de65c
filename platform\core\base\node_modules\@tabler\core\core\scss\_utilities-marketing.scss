$negative-spacers-extra: if(
  $enable-negative-margins,
  negativify-map(map-merge($spacers, $spacers-extra)),
  null
);

$utilities: (
  // Margin utilities
  "margin":
    (
      responsive: true,
      property: margin,
      class: m,
      values: $spacers-extra,
    ),
  "margin-x": (
    responsive: true,
    property: margin-right margin-left,
    class: mx,
    values: $spacers-extra,
  ),
  "margin-y": (
    responsive: true,
    property: margin-top margin-bottom,
    class: my,
    values: $spacers-extra,
  ),
  "margin-top": (
    responsive: true,
    property: margin-top,
    class: mt,
    values: $spacers-extra,
  ),
  "margin-end": (
    responsive: true,
    property: margin-right,
    class: me,
    values: $spacers-extra,
  ),
  "margin-bottom": (
    responsive: true,
    property: margin-bottom,
    class: mb,
    values: $spacers-extra,
  ),
  "margin-start": (
    responsive: true,
    property: margin-left,
    class: ms,
    values: $spacers-extra,
  ),
  // Negative margin utilities
  "negative-margin":
    (
      responsive: true,
      property: margin,
      class: m,
      values: $negative-spacers-extra,
    ),
  "negative-margin-x": (
    responsive: true,
    property: margin-right margin-left,
    class: mx,
    values: $negative-spacers-extra,
  ),
  "negative-margin-y": (
    responsive: true,
    property: margin-top margin-bottom,
    class: my,
    values: $negative-spacers-extra,
  ),
  "negative-margin-top": (
    responsive: true,
    property: margin-top,
    class: mt,
    values: $negative-spacers-extra,
  ),
  "negative-margin-end": (
    responsive: true,
    property: margin-right,
    class: me,
    values: $negative-spacers-extra,
  ),
  "negative-margin-bottom": (
    responsive: true,
    property: margin-bottom,
    class: mb,
    values: $negative-spacers-extra,
  ),
  "negative-margin-start": (
    responsive: true,
    property: margin-left,
    class: ms,
    values: $negative-spacers-extra,
  ),
  // Padding utilities
  "padding":
    (
      responsive: true,
      property: padding,
      class: p,
      values: $spacers-extra,
    ),
  "padding-x": (
    responsive: true,
    property: padding-right padding-left,
    class: px,
    values: $spacers-extra,
  ),
  "padding-y": (
    responsive: true,
    property: padding-top padding-bottom,
    class: py,
    values: $spacers-extra,
  ),
  "padding-top": (
    responsive: true,
    property: padding-top,
    class: pt,
    values: $spacers-extra,
  ),
  "padding-end": (
    responsive: true,
    property: padding-right,
    class: pe,
    values: $spacers-extra,
  ),
  "padding-bottom": (
    responsive: true,
    property: padding-bottom,
    class: pb,
    values: $spacers-extra,
  ),
  "padding-start": (
    responsive: true,
    property: padding-left,
    class: ps,
    values: $spacers-extra,
  ),
  // Gap utility
  "gap":
    (
      responsive: true,
      property: gap,
      class: gap,
      values: $spacers-extra,
    ),
  "row-gap": (
    responsive: true,
    property: row-gap,
    class: row-gap,
    values: $spacers-extra,
  ),
  "column-gap": (
    responsive: true,
    property: column-gap,
    class: column-gap,
    values: $spacers-extra,
  ),
  // Letter spacing
  "spacing":
    (
      property: letter-spacing,
      class: tracking,
      values: (
        tight: $spacing-tight,
        normal: $spacing-normal,
        wide: $spacing-wide,
      ),
    ),
  "width": (
    property: width,
    class: w,
    values: $spacers-extra,
  ),
  "height": (
    property: height,
    class: h,
    values: $spacers-extra,
  ),
  "filter": (
    property: filter,
    class: filter,
    values: (
      grayscale: grayscale(100%),
    ),
  ),
  "gutter-x": (
    responsive: true,
    css-var: true,
    css-variable-name: gutter-x,
    class: gx,
    values: $spacers-extra,
  ),
  "gutter-y": (
    responsive: true,
    css-var: true,
    css-variable-name: gutter-y,
    class: gy,
    values: $spacers-extra,
  ),
  "gutter": (
    responsive: true,
    css-var: true,
    css-variable-name: gutter-x,
    class: g,
    values: $spacers-extra,
  )
);
