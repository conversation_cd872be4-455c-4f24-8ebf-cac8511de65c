name: Close inactive issues and pr

on:
  schedule:
    - cron: "00 11 * * *"

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      issues: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 360
          days-before-issue-close: 14
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 360 days with no activity."
          close-issue-message: "This issue was closed because it has been inactive for 14 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          days-before-pr-stale: 360
          days-before-pr-close: 14
          stale-pr-label: "stale"
          stale-pr-message: "This pr is stale because it has been open for 360 days with no activity."
          close-pr-message: "This pr was closed because it has been inactive for 14 days since being marked as stale."    
