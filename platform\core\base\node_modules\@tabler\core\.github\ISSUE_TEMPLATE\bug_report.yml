name: Bug report
description: Create a report to help us improve
title: "[BUG] "
labels: ["bug"]
body:
- type: markdown
  attributes:
    value: "## Thank you for making a bug report!"
- type: markdown
  attributes:
    value: |
      Thanks for taking the time to fill out this bug! It's really important to fill this form out completely as
      not filling it out will make the bug reports hard to replicate.
- type: input
  id: browser
  attributes:
    label: Browser
    description: "What browser and version did this bug occur on?"
    placeholder: "e.g. Chrome ver.22, Safari ver.10"
  validations:
    required: true
- type: input
  id: os
  attributes:
    label: OS
    description: "What is the operating system of your device?"
    placeholder: "e.g. Windows 10, iOS 14, Ubuntu 23.04"
  validations:
    required: true
- type: input
  id: screen_size
  attributes:
    label: Screen size
    description: "What is the screen size of your device?"
    placeholder: "e.g. 800x600, 1920x1080"
  validations:
    required: true
- type: textarea
  id: description
  attributes:
    label: Describe the bug
    description: "A clear and concise description of what the bug is."
  validations:
    required: true
- type: textarea
  id: reproduce
  attributes:
    label: How to reproduce
    description: "How do you trigger this bug? Please walk us through it step by step."
    value: |
      1. Go to '...'
      2. Click on '...'
      3. Scroll down to '...'
      4. See error
      ...
  validations:
    required: true
- type: textarea
  id: screenshots
  attributes:
    label: Screenshots
    description: "If applicable, add screenshots here to help explain this problem. This helps us understand whats happening better."
  validations:
    required: false
- type: input
  id: jsfiddle
  attributes:
    label: JSFiddle
    description: "Please add a jsFiddle replicating the bug. Without the jsFiddle most bug reports cannot be solved and will be closed."
  validations:
    required: false
---
