.table-language thead,
.table-language tbody,
.table-language tr {
  width: 100% !important;
}
.table-language th,
.table-language td {
  text-align: center;
}
.table-language .text-start {
  text-align: left;
}
.table-language tr .set-language-default {
  opacity: 0.5;
}
.table-language tr:hover .set-language-default {
  opacity: 1;
}

.dataTable .language-header img {
  margin-inline-end: 0.5rem;
}
.dataTable .language-header img:last-child {
  margin-inline-end: 0;
}
.dataTable .language-header .language-column a {
  margin-inline-end: 0.5rem;
}
.dataTable .language-header .language-column a:last-child {
  margin-inline-end: 0;
}
.dataTable .language-header .language-column a:hover {
  text-decoration: none !important;
}
.dataTable .language-header .language-column a svg {
  max-width: none;
}
.dataTable .language-header span.dtr-title {
  margin-bottom: 0.5rem;
}

#list-others-language a {
  margin-bottom: 1rem;
}
#list-others-language a:last-child {
  margin-bottom: 0;
}
