name: Release

on:
  push:
    branches:
      - dev

permissions:
  contents: read
  pull-requests: write

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write # to create release 
      issues: write # to post issue comments
      pull-requests: write # to create pull request
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install PNPM
        uses: pnpm/action-setup@v4

      - name: Install Dependencies
        run: pnpm install

      - name: Build
        run: pnpm run build

      - name: Create release Pull Request or publish to NPM
        uses: changesets/action@v1
        with:
          version: pnpm run version
          publish: pnpm run publish
          commit: "chore: update versions"
          title: "chore: update versions"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}