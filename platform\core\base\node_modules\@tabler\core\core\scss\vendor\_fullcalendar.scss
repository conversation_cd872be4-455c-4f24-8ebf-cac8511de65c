:root {
  --fc-border-color: var(--#{$prefix}border-color);
  --fc-daygrid-event-dot-width: 5px;

  --fc-event-bg-color: var(--#{$prefix}primary-lt);
  --fc-event-border-color: var(--#{$prefix}primary-200);
  --fc-event-text-color: var(--#{$prefix}body-color);
}

.fc-toolbar-title {
  font-size: var(--#{$prefix}font-size-h3) !important;
  font-weight: var(--#{$prefix}font-weight-medium);
}

.fc-daygrid-dot-event {
  background-color: var(--fc-event-bg-color);
  border: 1px solid var(--fc-event-border-color);
  color: var(--fc-event-text-color);
  padding-top: 1px;
  padding-bottom: 1px;

  .fc-event-title {
    font-weight: inherit;
  }
}

.fc-col-header-cell {
  background-color: var(--#{$prefix}bg-surface-secondary);
  color: var(--#{$prefix}secondary);
  text-transform: uppercase;
  font-weight: var(--#{$prefix}font-weight-medium);
  font-size: var(--#{$prefix}font-size-h5);
  padding-top: .5rem !important;
  padding-bottom: .5rem !important;
  line-height: 1;
}

.fc-event-time {
  font-weight: var(--#{$prefix}font-weight-bold) !important;
}

.fc-col-header-cell-cushion {
  color: inherit;
}

.fc-daygrid-day-number {
  color: var(--#{$prefix}secondary);
}

.fc-button {
  color: var(--#{$prefix}body-color) !important;
  background-color: transparent !important;
  border-color: var(--#{$prefix}border-color) !important;
  font-weight: var(--#{$prefix}font-weight-medium) !important;
}