@use "@docsearch/css/dist/style.css";

:root {
  --docsearch-primary-color: var(--tblr-primary);
  --docsearch-searchbox-background: var(--tblr-bg-surface);
  --docsearch-text-color: var(--tblr-body-text);
  --docsearch-key-shadow: 0 0 0 1px var(--tblr-border-color);
  --docsearch-key-gradient: var(--tblr-bg-surface-secondary);
}

.col-docs {
  width: 15rem;
}

.bg-docs-gradient {
  @media (min-width: 992px) {
    background: radial-gradient(circle at 0 0, color-mix(in srgb, var(--tblr-primary) 4%, transparent), transparent 80%) no-repeat 0 0 / 800px 800px !important;
  }
}

.DocSearch-Button {
  width: 100%;
  box-shadow: 0 0 0 1px var(--tblr-border-color);
  font-weight: var(--tblr-font-weight-normal);
  transition: all 0.2s ease-in-out;
}

.DocSearch-Button-Key {
  top: 0;
}

.DocSearch-Container {
  z-index: 10000;
}

.no-transition {
  * {
    transition: none !important;
  }
}

.example > .modal,
.example > .offcanvas {
  display: block !important;
  position: relative !important;
}

.example > .offcanvas-backdrop {
  position: absolute !important;
}

.shiki {
  background: var(--tblr-gray-900) !important;
  color: var(--tblr-gray-300) !important;
}

code {
  ::selection {
    background: var(--tblr-primary);
  }
}
