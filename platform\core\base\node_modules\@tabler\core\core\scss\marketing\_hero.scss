//
// Hero
//
.hero {
  text-align: center;
  padding: 6.5rem 0;
}

.hero-title {
  font-size: 3rem;
  font-weight: var(--#{$prefix}font-weight-black);
  letter-spacing: $spacing-tight;
  line-height: $headings-line-height;

  @include media-breakpoint-down(md) {
    font-size: 2rem;
  }
}

.hero-description {
  color: var(--#{$prefix}secondary);
  font-size: var(--#{$prefix}font-size-h2);
  line-height: 1.5;
  margin: 0 auto;
  max-width: 45rem;

  @include media-breakpoint-down(sm) {
    font-size: var(--#{$prefix}font-size-h3);
  }
}

.hero-description-wide {
  max-width: 61.875rem;
}

//
// Hero subheader
//
.hero-subheader {
  @include subheader;
  margin-bottom: 0.5rem;
}

.hero-img {
  margin: 4rem auto;
  max-width: 65rem;
  border-radius: $border-radius-lg;
  position: relative;
  z-index: 1;
  //box-shadow: 0 10px 15px -3px rgba($color-text, 0.1),
  //  0 4px 6px -2px rgba($color-text, 0.05);

  img,
  svg {
    max-width: 100%;
    height: auto;
    display: block;
    position: relative;
  }
}
//
//.hero-img-side {
//  img,
//  svg {
//    max-width: 100%;
//    height: auto;
//    display: block;
//  }
//}
