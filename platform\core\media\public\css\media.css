@charset "UTF-8";
/*Features*/
/*
|
| When you have a container with fixed width
| and you want the background of this container
| is full
|
*/
.rv-media-breadcrumb {
  flex-grow: 1;
}
.rv-media-breadcrumb ul {
  margin: 0;
  padding: 0;
}
.rv-media-breadcrumb .breadcrumb {
  background-color: transparent;
  margin-bottom: 0;
}
.rv-media-breadcrumb .breadcrumb li:last-child:not(:first-child) a {
  --bb-text-opacity: 1;
  color: rgba(var(--bb-muted-rgb), var(--bb-text-opacity)) !important;
}
.rv-media-breadcrumb .breadcrumb > li + li:before {
  padding: 0 5px;
  color: #ccc;
  content: "/ ";
}

.rv-media-block {
  padding: 15px;
}
.rv-media-block .rv-media-block-title {
  text-transform: uppercase;
  font-weight: 700;
  font-size: 13px;
}

.rv-media-container .progress {
  box-shadow: none;
  height: 10px;
  margin: 0;
}
.rv-media-container .progress-bar-warning {
  background-color: #f7ca18;
}
.rv-media-container .progress-bardanger {
  background-color: #f24d23;
}
.rv-media-container .progress-bar-success {
  background-color: #003d4f;
}
.rv-media-container .progress-bar-info {
  background-color: #dbdbdb;
}

.rv-media-container .custom-checkbox {
  position: relative;
}
.rv-media-container .custom-checkbox label {
  margin: 0;
  display: block;
  cursor: pointer;
}
.rv-media-container .custom-checkbox input {
  width: 0;
  height: 0;
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}
.rv-media-container .custom-checkbox span {
  width: 20px;
  height: 20px;
  border: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color-translucent);
  display: block;
  position: relative;
  border-radius: var(--bb-border-radius);
  box-shadow: var(--bb-box-shadow-input);
}
.rv-media-container .custom-checkbox span:before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-size: 0;
  font-weight: 600;
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--bb-white);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease-in-out;
}
.rv-media-container .custom-checkbox input:checked + span {
  background-color: var(--bb-primary);
}
.rv-media-container .custom-checkbox input:checked + span:before {
  font-size: 13px;
}

.rv-media-thumbnail {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.rv-media-thumbnail:before {
  content: "";
  display: block;
  padding-bottom: 100%;
  height: 0;
}
.rv-media-thumbnail svg {
  font-size: 30px;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
.rv-media-thumbnail img {
  max-height: 100%;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.rv-upload-progress {
  box-shadow: 0 0 4px rgba(58, 85, 113, 0.3);
  width: 600px;
  z-index: 9999;
  transition: all 0.3s linear;
}
.rv-upload-progress.hide-the-pane {
  transform: translateX(100%);
  opacity: 0;
  filter: alpha(opacity=0);
}
.rv-upload-progress .close-pane {
  right: 12px;
  margin-top: -12px;
  transition: all 0.3s ease-in-out;
}
.rv-upload-progress .close-pane:hover {
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.rv-upload-progress .close-pane svg {
  margin: 0;
}

.rv-media-wrapper .btn.collapse-panel i {
  transition: all 0.3s ease-in-out;
}

.rv-media-header .rv-media-top-header .rv-media-search {
  width: 100%;
}
@media (min-width: 768px) {
  .rv-media-header .rv-media-top-header .rv-media-search {
    width: initial;
  }
}

.rv-media-footer {
  padding: 10px;
  text-align: right;
  border-top: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
}

#rv_media_body {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 8rem);
}

.navbar .dropdown-menu-right {
  right: 0;
  left: auto;
}

.rv-media-container {
  width: 100%;
  min-height: 100%;
  height: 100%;
  font-size: 13px;
  overflow: auto;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
}
.rv-media-container .form-add-folder .input-group-text {
  padding: 0;
}
.rv-media-container .rv-media-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  flex-grow: 1;
}
.rv-media-container .rv-media-wrapper .rv-media-aside,
.rv-media-container .rv-media-wrapper .rv-media-main-wrapper {
  align-items: stretch;
}
.rv-media-container .rv-media-wrapper .rv-media-main-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.rv-media-container .rv-media-wrapper .rv-media-main-wrapper .rv-media-main {
  flex-grow: 1;
  flex-direction: row;
  display: flex;
  height: 200px;
}
.rv-media-container .rv-media-wrapper .rv-media-main-wrapper .rv-dropdown-actions .dropdown-toggle:after {
  display: none;
}
.rv-media-container .rv-media-items,
.rv-media-container .rv-media-details {
  align-items: stretch;
  display: flex;
  overflow: auto;
  position: relative;
}
.rv-media-container .rv-media-items {
  flex-grow: 1;
  flex-direction: column;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.rv-media-container .rv-media-items .rv-media-item {
  overflow: hidden;
  background-color: var(--bb-body-bg);
}
.rv-media-container .rv-media-details {
  width: 250px;
  min-width: 250px;
  border-left: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
  flex-direction: column;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  overflow-y: auto;
}
.rv-media-container .rv-media-details .rv-media-thumbnail,
.rv-media-container .rv-media-details .rv-media-description {
  width: 250px;
  min-width: 250px;
}
.rv-media-container .rv-media-details .rv-media-thumbnail {
  border-bottom: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
  overflow: visible;
}
.rv-media-container .rv-media-details .rv-media-thumbnail svg {
  width: 6rem;
  height: 6rem;
}
.rv-media-container .rv-media-details .rv-media-thumbnail img {
  max-width: 90%;
}
.rv-media-container .rv-media-details .rv-media-description {
  padding: 10px;
  position: relative;
}
.rv-media-container .rv-media-details .rv-media-description .rv-media-name > p {
  font-weight: 700;
  margin: 0;
}
.rv-media-container .rv-media-details .rv-media-description .rv-media-name + .rv-media-name {
  margin-top: 5px;
}
.rv-media-container #media_details_collapse:checked ~ .rv-media-main-wrapper .rv-media-details {
  width: 0;
  min-width: 0;
  border: 0 none;
}
.rv-media-container #media_details_collapse:checked ~ .rv-media-main-wrapper .rv-media-tools .collapse-panel svg {
  transform: rotate(-180deg);
}
@media (max-width: 1365px) {
  .rv-media-container .rv-media-details {
    width: 220px;
    min-width: 220px;
  }
  .rv-media-container .rv-media-details .rv-media-thumbnail,
  .rv-media-container .rv-media-details .rv-media-description {
    width: 220px;
    min-width: 220px;
  }
}
@media (max-width: 991px) {
  .rv-media-container .rv-media-wrapper .rv-media-main-wrapper {
    width: 100%;
  }
  .rv-media-container .rv-media-details {
    display: none !important;
  }
}

.media-download-popup {
  position: absolute;
  bottom: 0;
  right: 24px;
}

/*Table*/
.rv-media-list {
  border-bottom: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
}
.rv-media-list ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.rv-media-list .rv-media-list-title {
  display: flex;
  flex-direction: row;
  padding: 15px 10px;
  cursor: pointer;
}
.rv-media-list .rv-media-list-title .custom-checkbox {
  width: 35px;
  min-width: 35px;
}
.rv-media-list .rv-media-list-title .rv-media-file-name {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 15px;
}
.rv-media-list .rv-media-list-title .rv-media-file-name i {
  margin-right: 5px;
}
.rv-media-list .rv-media-list-title .rv-media-file-name i:before {
  display: inline-block;
  font-size: 18px;
}
.rv-media-list .rv-media-list-title .rv-media-file-size {
  width: 120px;
  min-width: 120px;
}
.rv-media-list .rv-media-list-title .rv-media-created-at {
  width: 150px;
  min-width: 150px;
}
.rv-media-list .rv-media-list-title:nth-child(odd) {
  background-color: var(--bb-bg-surface-tertiary);
}
.rv-media-list .rv-media-list-title:hover {
  background-color: var(--bb-bg-surface-secondary);
}

/*Grid*/
.rv-media-grid {
  padding: 10px 10px 0;
}
.rv-media-grid ul {
  padding: 0;
  margin: 0 -5px;
}
.rv-media-grid ul:before, .rv-media-grid ul:after {
  content: "";
  display: table;
  clear: both;
}
.rv-media-grid li {
  display: block;
  width: 12.5%;
  margin-bottom: 15px;
  float: left;
  padding: 0 5px;
}
.rv-media-grid li .rv-media-item {
  position: relative;
  cursor: pointer;
  transition: all 0.1s ease-in-out;
}
.rv-media-grid li .rv-media-item:after {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
}
.rv-media-grid li .rv-media-item .media-item-selected {
  display: none;
  position: absolute;
  top: 3px;
  right: 3px;
  border-radius: 50% !important;
  background: #007bff;
  height: 24px;
  width: 24px;
  z-index: 20;
  text-align: center;
}
.rv-media-grid li .rv-media-item .media-item-selected svg {
  fill: white;
  width: 18px;
  margin-top: 3px;
}
.rv-media-grid li .rv-media-thumbnail {
  border-bottom: 0 none;
}
.rv-media-grid li .rv-media-thumbnail img {
  width: auto;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
.rv-media-grid li .rv-media-description {
  padding: 8px 10px;
  transition: all 0.1s ease-in-out;
  background-color: var(--bb-bg-surface-secondary);
  text-align: center;
}
.rv-media-grid li .rv-media-description .title {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.rv-media-grid li .rv-media-description .size {
  font-size: 12px;
  margin-top: 10px;
}
.rv-media-grid li input[type=checkbox]:checked + .rv-media-item .media-item-selected {
  display: block;
}
.rv-media-grid li input[type=checkbox]:checked + .rv-media-item:after {
  border: var(--bb-border-width) var(--bb-border-style) var(--bb-primary);
  background: rgba(var(--bb-primary-rgb), 0.04);
  transition: border-color 0.3s, background 0.3s, color 0.3s;
}
.rv-media-grid li input[type=checkbox]:checked + .rv-media-item .rv-media-description {
  background-color: #007bff;
  color: #ffffff;
}

.rv-media-grid-small-thumbnail li {
  width: 33.3333333333%;
}
.rv-media-grid-small-thumbnail li .rv-media-item {
  display: flex;
  flex-direction: row;
}
.rv-media-grid-small-thumbnail li .rv-media-thumbnail {
  width: 100px;
  min-width: 100px;
  border: 0 none;
  left: 0;
  top: 0;
}
.rv-media-grid-small-thumbnail li .rv-media-description {
  flex-grow: 1;
}

.rv-media-grid:not(.rv-media-grid-small-thumbnail) .rv-media-thumbnail {
  text-align: center;
}
.rv-media-grid:not(.rv-media-grid-small-thumbnail) .rv-media-thumbnail svg {
  width: 3rem;
  height: 3rem;
}

.rv-media-items li.no-items {
  width: auto;
  height: auto;
  border-radius: 0;
  border: 0 none;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #94a7b1;
  font-weight: 400;
  margin: 0 !important;
  text-align: center;
}
.rv-media-items li.no-items .icon {
  --bb-icon-size: 4rem;
  margin-bottom: 1rem;
}
.rv-media-items li.no-items h3 {
  font-size: 14px;
  margin-bottom: 0.25rem;
}
.on-loading .rv-media-items li.no-items {
  display: none !important;
}
.rv-media-items.has-items .no-items {
  display: none !important;
}
.rv-media-container[data-breadcrumb-count="0"] .rv-media-items li.up-one-level, .rv-media-container[data-breadcrumb-count="1"] .rv-media-items li.up-one-level {
  display: none !important;
}
.rv-media-items li.up-one-level {
  display: none;
}
@media (min-width: 768px) {
  .rv-media-items li.up-one-level {
    display: block;
  }
}
.rv-media-container:not([data-view-in=all_media]) .rv-media-items li.no-items {
  pointer-events: none;
}
.rv-media-container[data-view-in=all_media] .rv-media-items li.no-items {
  width: 200px;
  height: 200px;
  border-radius: 50% !important;
  border: 1px solid #dce6f1;
  background-color: #ffffff;
  cursor: pointer;
}
@media (min-width: 992px) {
  .rv-media-container[data-view-in=all_media] .rv-media-items li.no-items {
    width: 400px;
    height: 400px;
  }
}

@media (min-width: 768px) {
  .rv-media-items li.no-items .icon {
    --bb-icon-size: 8rem;
  }
  .rv-media-items li.no-items h3 {
    font-size: 17px;
  }
}
@media (max-width: 1365px) {
  .rv-media-grid li {
    width: 16.6666666667%;
  }
}
@media (max-width: 991px) {
  .rv-media-grid li {
    width: 33.3333333333%;
  }
}
.context-menu-list {
  z-index: 21 !important;
}

.rv-media-breadcrumb .breadcrumb li:first-child:before {
  display: none !important;
}

.rv-media-container {
  height: calc(100vh - 16rem);
}
.rv-media-container .js-rv-media-filter-current .icon {
  margin-inline-end: 2px;
}
.rv-media-container .js-rv-media-filter-current .dropdown-item-icon {
  color: unset;
  margin-right: unset;
  opacity: unset;
}

@media (max-width: 991px) {
  .rv-media-container .rv-media-aside {
    z-index: 10000;
  }
}
.rv-media-integrate-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.rv-media-modal .modal-open .context-menu-root {
  z-index: 99994 !important;
}

/*!
 * Cropper v0.7.1
 * https://github.com/fengyuanchen/cropper
 *
 * Copyright 2014 Fengyuan Chen
 * Released under the MIT license
 */
.cropper-container {
  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
.cropper-container img {
  width: 100%;
  max-width: none !important;
  height: 100%;
  max-height: none !important;
}

.cropper-modal,
.cropper-canvas {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cropper-canvas {
  background-color: #ffffff;
  filter: alpha(opacity=0);
  opacity: 0;
}

.cropper-modal {
  background-color: #000000;
  filter: alpha(opacity=50);
  opacity: 0.5;
}

.cropper-dragger {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
}

.cropper-viewer {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline-width: 1px;
  outline-style: solid;
  outline-color: #6699ff;
  outline-color: rgba(51, 102, 255, 0.75);
}

.cropper-dashed {
  position: absolute;
  display: block;
  filter: alpha(opacity=50);
  border: 0 dashed #ffffff;
  opacity: 0.5;
}
.cropper-dashed.dashed-h {
  top: 33.3%;
  left: 0;
  width: 100%;
  height: 33.3%;
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.cropper-dashed.dashed-v {
  top: 0;
  left: 33.3%;
  width: 33.3%;
  height: 100%;
  border-right-width: 1px;
  border-left-width: 1px;
}

.cropper-face,
.cropper-line,
.cropper-point {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=10);
  opacity: 0.1;
}

.cropper-face {
  top: 0;
  left: 0;
  cursor: move;
  background-color: #ffffff;
}

.cropper-line {
  background-color: #6699ff;
}
.cropper-line.line-e {
  top: 0;
  right: -3px;
  width: 5px;
  cursor: e-resize;
}
.cropper-line.line-n {
  top: -3px;
  left: 0;
  height: 5px;
  cursor: n-resize;
}
.cropper-line.line-w {
  top: 0;
  left: -3px;
  width: 5px;
  cursor: w-resize;
}
.cropper-line.line-s {
  bottom: -3px;
  left: 0;
  height: 5px;
  cursor: s-resize;
}

.cropper-point {
  width: 5px;
  height: 5px;
  background-color: #6699ff;
  filter: alpha(opacity=75);
  opacity: 0.75;
}
.cropper-point.point-e {
  top: 50%;
  right: -3px;
  margin-top: -3px;
  cursor: e-resize;
}
.cropper-point.point-n {
  top: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}
.cropper-point.point-w {
  top: 50%;
  left: -3px;
  margin-top: -3px;
  cursor: w-resize;
}
.cropper-point.point-s {
  bottom: -3px;
  left: 50%;
  margin-left: -3px;
  cursor: s-resize;
}
.cropper-point.point-ne {
  top: -3px;
  right: -3px;
  cursor: ne-resize;
}
.cropper-point.point-nw {
  top: -3px;
  left: -3px;
  cursor: nw-resize;
}
.cropper-point.point-sw {
  bottom: -3px;
  left: -3px;
  cursor: sw-resize;
}
.cropper-point.point-se {
  right: -3px;
  bottom: -3px;
  width: 20px;
  height: 20px;
  cursor: se-resize;
  filter: alpha(opacity=100);
  opacity: 1;
}
.cropper-point.point-se:before {
  position: absolute;
  right: -50%;
  bottom: -50%;
  display: block;
  width: 200%;
  height: 200%;
  content: " ";
  background-color: #6699ff;
  filter: alpha(opacity=0);
  opacity: 0;
}

@media (min-width: 768px) {
  .cropper-point.point-se {
    width: 15px;
    height: 15px;
  }
}
@media (min-width: 992px) {
  .cropper-point.point-se {
    width: 10px;
    height: 10px;
  }
}
@media (min-width: 1200px) {
  .cropper-point.point-se {
    width: 5px;
    height: 5px;
    filter: alpha(opacity=75);
    opacity: 0.75;
  }
}
.cropper-hidden {
  display: none !important;
}

.cropper-invisible {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: auto !important;
  max-width: none !important;
  height: auto !important;
  max-height: none !important;
  filter: alpha(opacity=0);
  opacity: 0;
}

.cropper-move {
  cursor: move;
}

.cropper-crop {
  cursor: crosshair;
}

.cropper-disabled .cropper-canvas,
.cropper-disabled .cropper-face,
.cropper-disabled .cropper-line,
.cropper-disabled .cropper-point {
  cursor: not-allowed;
}

.avatar-body {
  padding-right: 15px;
  padding-left: 15px;
}

.avatar-upload {
  overflow: hidden;
}
.avatar-upload label {
  display: block;
  float: left;
  clear: left;
  width: 100px;
}
.avatar-upload input {
  display: block;
  margin-left: 110px;
}

.avater-alert {
  margin-top: 10px;
  margin-bottom: 10px;
}

.avatar-wrapper {
  height: 364px;
  width: 100%;
  margin-top: 15px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.25);
  background-color: #fcfcfc;
  overflow: hidden;
}
.avatar-wrapper img {
  display: block;
  height: auto;
  max-width: 100%;
}

.avatar-preview {
  float: left;
  margin-top: 15px;
  margin-right: 15px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  background-color: #ffffff;
  overflow: hidden;
}
.avatar-preview:hover {
  border-color: #ccccff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
}
.avatar-preview img {
  width: 100%;
}

.preview-lg {
  height: 184px;
  width: 184px;
  margin-top: 15px;
}

.preview-md {
  height: 100px;
  width: 100px;
}

.preview-sm {
  height: 50px;
  width: 50px;
}

@media (min-width: 992px) {
  .avatar-preview {
    float: none;
  }
}
.cropper-loading {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #ffffff url("/vendor/core/core/base/images/loading.gif") no-repeat center center;
  opacity: 0.75;
  filter: alpha(opacity=75);
  z-index: 20140628;
}

.avatar-view {
  cursor: pointer;
}
