.shortcode-list-modal .modal-body {
  background-color: var(--bb-body-bg);
}
.shortcode-list-modal .shortcode-item-input:checked ~ .shortcode-item {
  border-color: var(--bb-primary);
}
.shortcode-list-modal .shortcode-item-input:checked ~ .shortcode-item .checked-icon {
  display: block;
}
.shortcode-list-modal .shortcode-item-input:checked ~ .shortcode-item .card {
  border-color: transparent;
}
.shortcode-list-modal .shortcode-item {
  position: relative;
  cursor: pointer;
  border: 2px transparent solid;
  border-radius: 4px;
}
.shortcode-list-modal .shortcode-item .checked-icon {
  display: none;
  position: absolute;
  color: #fff;
  top: 15px;
  right: 15px;
  border-radius: 50%;
  background-color: var(--bb-primary);
  padding: 3px;
  z-index: 10;
}
.shortcode-list-modal .shortcode-item .image-wrapper {
  padding-top: 56.25%;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px var(--bb-card-border-color) solid;
}
.shortcode-list-modal .shortcode-item .image-wrapper > img {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
  -o-object-fit: contain;
     object-fit: contain;
}
.shortcode-list-modal .shortcode-item .image-wrapper .large > img {
  opacity: 0;
  z-index: -1;
  position: absolute;
}
.shortcode-list-modal .shortcode-item .image-wrapper:hover .large > img {
  opacity: 1;
  z-index: 9999;
}
.shortcode-list-modal .shortcode-item .card-header {
  border-bottom: unset;
}
.shortcode-list-modal .shortcode-item .card-header .card-title,
.shortcode-list-modal .shortcode-item .card-header .card-subtitle {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}
.shortcode-list-modal .shortcode-item:hover .card-header {
  background-color: var(--bb-body-bg);
}
.shortcode-list-modal .shortcode-item .use-button {
  --bb-btn-padding-y: 0.25rem !important;
  --bb-btn-padding-x: 0.5rem !important;
  --bb-btn-font-size: 0.75rem !important;
}
