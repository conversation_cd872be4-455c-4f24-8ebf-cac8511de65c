/**
Markdown
 */
.markdown {
  line-height: $line-height-xl;

  > :first-child {
    margin-top: 0;
  }

  > :last-child,
  > :last-child .highlight {
    margin-bottom: 0;
  }

  > hr {
    @include media-breakpoint-up(md) {
      margin-top: 3em;
      margin-bottom: 3em;
    }
  }

  > {
    h1, h2, h3, h4, h5, h6 {
      font-weight: var(--#{$prefix}font-weight-bold);
    }

    h2, h3, h4, h5, h6 {
      margin-top: 2.5rem;
    }
  }

  > table {
    font-size: var(--#{$prefix}body-font-size);
    @extend .table, .table-bordered, .table-sm;
  }

  > blockquote {
    font-size: $h3-font-size;
    margin: 1.5rem 0;
    padding: .5rem 1.5rem;
  }

  > img,
  > p > img {
    border-radius: var(--#{$prefix}border-radius);
    border: 1px solid var(--#{$prefix}border-color);
  }

  pre {
    max-height: 20rem;
  }
}