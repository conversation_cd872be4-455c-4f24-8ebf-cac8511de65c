body {
  background: rgb(63, 94, 251);
  background: linear-gradient(45deg, rgb(63, 94, 251) 0%, rgb(252, 70, 107) 100%);
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.min-h-full {
  min-height: 100vh;
}

.installer-container {
  max-width: 1024px !important;
}
.installer-container .installer-title {
  font-size: 36px;
}
.installer-container .installer-wrapper {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  border-radius: 4px;
  overflow: hidden;
}
.installer-container .installer-wrapper .steps .step-item.active ~ .step-item {
  color: #989ea7;
}
.installer-container .installer-wrapper .steps .step-item.active ~ .step-item:before {
  background: #d3d5d8;
}
.installer-container .installer-wrapper .card {
  border-radius: unset;
}
.installer-container .installer-wrapper .card .card-header {
  border-bottom: 0;
}
.installer-container .installer-wrapper .card .card-header .card-title {
  font-size: 18px;
  text-align: center;
}
.installer-container .installer-wrapper .card .card-body {
  min-height: 320px;
}
.installer-container .installer-wrapper .steps-vertical .step-item {
  font-size: 14px;
  padding-top: 3px;
}
.installer-container .installer-wrapper .steps-vertical .step-item:not(:first-child) {
  margin-top: 30px;
}
.installer-container .installer-wrapper .steps-vertical .step-item:not(:first-child):after {
  height: calc(100% + 30px);
}
.installer-container .installer-wrapper .steps-vertical .step-item:not(:last-child):after {
  height: calc(100% + 30px);
  left: calc(var(--bb-steps-dot-size) * 0.65);
}
.installer-container .installer-wrapper .steps-vertical .step-item:before {
  padding: 4px;
  font-size: 18px;
}
