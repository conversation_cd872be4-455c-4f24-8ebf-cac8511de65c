.list-photos-gallery {
  list-style: none;
  padding-left: 0;
}
.list-photos-gallery > .row > .photo-gallery-item {
  margin-bottom: 10px;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper {
  position: relative;
  overflow: hidden;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper img {
  border: 1px solid #ddd;
  width: 100%;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.25s ease-in-out;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper::before {
  content: "\f044";
  display: inline-block;
  font: normal normal normal 14px/1 "Font Awesome 6 Free";
  font-weight: 400;
  font-size: 18px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate(0, 0);
  position: absolute;
  top: 48%;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
  color: #fff;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.25s ease-in-out;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper:hover {
  cursor: pointer;
}
.list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper:hover::after, .list-photos-gallery > .row > .photo-gallery-item .gallery_image_wrapper:hover::before {
  opacity: 1;
}
