.owl-carousel {
  position: relative;
}
.owl-carousel .owl-item img {
  width: 100%;
}

.carousel--nav {
  position: relative;
  z-index: 10;
}
.carousel--nav .owl-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  width: 100%;
  height: 0;
  display: none;
}
.carousel--nav .owl-nav > * {
  display: inline-block;
  position: relative;
  vertical-align: top;
  width: 50px;
  height: 50px;
  background-color: transparent;
  transition: all 0.4s ease;
  transform: translateY(-50%);
}
.carousel--nav .owl-nav > * i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 30px;
  color: #282828;
}
.carousel--nav .owl-nav > :hover {
  box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}
.carousel--nav .owl-nav .owl-prev {
  margin-left: 40px;
}
.carousel--nav .owl-nav .owl-next {
  float: right;
  margin-right: 40px;
}
.carousel--nav .owl-dots {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  text-align: center;
}
.carousel--nav .owl-dots .owl-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 8px;
  border: 1px solid #bebebe;
  background-color: transparent;
  border-radius: 50%;
}
.carousel--nav .owl-dots .owl-dot:last-child {
  margin-right: 0;
}
.carousel--nav .owl-dots .owl-dot.active {
  background-color: #000000;
  border-color: #000000;
}
.carousel--nav.inside .owl-nav {
  z-index: 20;
  height: 0;
}
.carousel--nav.inside .owl-nav > * {
  transform: translateY(-50%);
}
.carousel--nav.inside .owl-prev {
  margin-left: 0;
}
.carousel--nav.inside .owl-prev i {
  padding-right: 5px;
}
.carousel--nav.inside .owl-next {
  float: right;
  margin-right: 0;
}
.carousel--nav.inside .owl-next i {
  padding-left: 5px;
}

@media (min-width: 1200px) {
  .carousel--nav .owl-nav {
    display: block;
  }
}
.slider-item {
  position: relative;
  max-height: 400px;
  overflow: hidden;
  margin: 10px 0;
}
.slider-item .slider-item-header {
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% - 40px);
  padding: 20px;
  z-index: 20;
  background-color: rgba(0, 0, 0, 0.6);
}
.slider-item .slider-item-header .slider-item-title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
}
.slider-item .slider-item-header .slider-item-description {
  color: #eeeeee;
}
.slider-item .slider-item-header .slider-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
