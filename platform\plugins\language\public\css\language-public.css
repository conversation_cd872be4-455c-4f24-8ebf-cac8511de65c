.language-wrapper .dropdown {
  height: 100%;
}
.language-wrapper .dropdown.open .language_bar_chooser.dropdown-menu {
  display: block;
}
.language-wrapper .dropdown .btn {
  border: none !important;
  background: none !important;
  margin-bottom: 0;
  border-radius: 0 !important;
  padding: 7px 15px;
  outline: none !important;
  box-shadow: none !important;
  transition: all 0.4s ease;
  color: inherit !important;
  font-size: inherit;
  height: 100%;
}
.language-wrapper .dropdown .dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #ffffff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  width: 160px;
  line-height: 58px;
  margin: 0;
  padding: 0;
}
.language-wrapper .dropdown .dropdown-menu li img {
  margin: 0 10px;
}
.language-wrapper .dropdown .dropdown-menu li span {
  color: #222222;
}
.language-wrapper .dropdown .dropdown-menu li.active a span {
  color: #fff;
}
.language-wrapper .dropdown .language_bar_chooser {
  display: block;
  float: right;
  width: 160px;
  line-height: 58px;
  text-align: center;
}
.language-wrapper .dropdown .language_bar_chooser li {
  display: inline-block;
  float: left;
  margin-left: 5px;
}
.language-wrapper .dropdown .language_bar_chooser.dropdown-menu {
  display: none;
  border-radius: 0 !important;
}
.language-wrapper .dropdown .language_bar_chooser.dropdown-menu li {
  width: 100%;
  margin: 0;
  display: block;
}
.language-wrapper .dropdown .language_bar_chooser.dropdown-menu li:hover {
  background: #dddddd;
}
.language-wrapper .dropdown .language_bar_chooser.dropdown-menu li a {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 0 !important;
  width: 100%;
  text-align: left;
}
.language-wrapper .language_bar_list li {
  display: inline-block;
  margin-left: 5px;
  min-width: 90px;
}
.language-wrapper .language_bar_list li a {
  width: 100%;
  align-items: center;
  display: flex;
  justify-content: center;
}
.language-wrapper .language_bar_list li a img {
  margin-right: 10px;
  width: 20px;
}
.language-wrapper .language_bar_list li a span {
  color: #222222;
}

body[dir=rtl] .language-wrapper .language_bar_list li a img {
  margin-right: 0;
  margin-left: 10px;
}
body[dir=rtl] .language-wrapper .language_bar_chooser.dropdown-menu li a {
  text-align: right;
}
