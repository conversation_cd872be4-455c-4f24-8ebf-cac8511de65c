:root,
:host {
  font-size: 16px;
  height: 100%;

  @include media-breakpoint-up(lg) {
    margin-left: calc(100vw - 100%);
    margin-right: 0;
  }
}

:root,
:host,
[data-bs-theme="light"] {
  color-scheme: light;
  --#{$prefix}spacer: var(--#{$prefix}spacer-2);

  --#{$prefix}bg-surface: var(--#{$prefix}bg-surface-primary);
  --#{$prefix}bg-surface-primary: var(--#{$prefix}white);
  --#{$prefix}bg-surface-secondary: var(--#{$prefix}gray-50);
  --#{$prefix}bg-surface-tertiary: var(--#{$prefix}gray-50);
  --#{$prefix}bg-surface-dark: var(--#{$prefix}gray-900);
  --#{$prefix}bg-surface-inverted: var(--#{$prefix}gray-900);
  --#{$prefix}bg-forms: var(--#{$prefix}bg-surface);

  --#{$prefix}text-inverted: var(--#{$prefix}gray-100);

  --#{$prefix}body-color: var(--#{$prefix}gray-700);
  --#{$prefix}body-bg: var(--#{$prefix}bg-surface-secondary);

  --#{$prefix}link-color: var(--#{$prefix}primary);
  --#{$prefix}link-hover-color: color-mix(in srgb, var(--#{$prefix}primary), #000 20%);

  --#{$prefix}secondary: var(--#{$prefix}gray-500);
  --#{$prefix}tertiary: var(--#{$prefix}gray-400);

  --#{$prefix}border-color: #{$border-color};
  --#{$prefix}border-color-translucent: #{$border-color-translucent};
  --#{$prefix}border-dark-color: #{$border-dark-color};
  --#{$prefix}border-dark-color-translucent: #{$border-dark-color-translucent};
  --#{$prefix}border-active-color: #{$border-active-color};

  --#{$prefix}icon-color: #{$icon-color};

  --#{$prefix}active-bg: #{$active-bg};

  --#{$prefix}disabled-bg: #{$disabled-bg};
  --#{$prefix}disabled-color: #{$disabled-color};

  --#{$prefix}code-color: #{$code-color};
  --#{$prefix}code-bg: #{$code-bg};

  --#{$prefix}dark-mode-border-color: #{$border-color-dark};
  --#{$prefix}dark-mode-border-color-translucent: #{$border-color-translucent-dark};
  --#{$prefix}dark-mode-border-active-color: #{$border-active-color-dark};
  --#{$prefix}dark-mode-border-dark-color: #{$border-dark-color-dark};

  --#{$prefix}page-padding: #{$page-padding};
  --#{$prefix}page-padding-y: #{$page-padding-y};

  @include media-breakpoint-down($cards-grid-breakpoint) {
    --#{$prefix}page-padding: #{$page-padding-sm};
  }
}
