name: Bundlewatch

on:
  pull_request: null
  push:
    branches:
      - main
      - dev

env:
  FORCE_COLOR: 2
  NODE: 20

jobs:
  bundlewatch:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with: 
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "${{ env.NODE }}"
     
      - name: Install PNPM
        uses: pnpm/action-setup@v4

      - name: Set up Bund<PERSON>
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.2
          bundler-cache: true

      - name: Install pnpm dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Run bundlewatch
        run: pnpm run bundlewatch
        env:
          BUNDLEWATCH_GITHUB_TOKEN: "${{ secrets.BUNDLEWATCH_GITHUB_TOKEN }}"
          CI_BRANCH_BASE: dev
