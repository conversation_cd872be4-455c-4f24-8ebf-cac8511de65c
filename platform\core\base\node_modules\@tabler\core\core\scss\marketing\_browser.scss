//
// Browser
//
.browser {
  border-radius: var(--#{$prefix}border-radius-lg);
  box-shadow: 0 0 0 1px var(--#{$prefix}border-color);
  background: var(--#{$prefix}bg-surface-secondary);
  overflow: hidden;
}

.browser-header {
  padding: .25rem 1rem;
  background: var(--#{$prefix}border-color-light) linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, .03));
  border-bottom: 1px solid var(--#{$prefix}border-color);
  border-radius: calc(var(--#{$prefix}border-radius-lg) - 1px) calc(var(--#{$prefix}border-radius-lg) - 1px) 0 0;
}

.browser-dots {
  margin-right: 3rem;
  display: flex;
}

.browser-dots-colored {
  .browser-dot {
    &:nth-child(1) {
      background: #fb6058;
    }

    &:nth-child(2) {
      background: #fcbe3b;
    }

    &:nth-child(3) {
      background: #2ccb4c;
    }
  }
}

.browser-dot {
  margin-right: .5rem;
  width: .75rem;
  min-width: .75rem;
  height: .75rem;
  background: var(--#{$prefix}border-color);
  border-radius: 50%;
  border: 1px solid var(--#{$prefix}border-color-dark);
}

.browser-input {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  padding: .25rem;
  color: var(--#{$prefix}secondary);
  font-size: var(--#{$prefix}font-size-h5);
  border-radius: var(--#{$prefix}border-radius);
  line-height: 1;
  cursor: pointer;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-image: linear-gradient(to bottom, var(--#{$prefix}bg-surface), var(--#{$prefix}bg-surface-secondary));

  &:hover {
    text-decoration: none;
  }
}
