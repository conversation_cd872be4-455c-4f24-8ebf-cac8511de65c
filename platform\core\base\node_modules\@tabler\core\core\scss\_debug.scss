$debug: false;

@if $debug {
  $colors: (
    "blue": $blue,
    "azure": $azure,
    "indigo": $indigo,
    "purple": $purple,
    "pink": $pink,
    "red": $red,
    "orange": $orange,
    "yellow": $yellow,
    "lime": $lime,
    "green": $green,
    "teal": $teal,
    "cyan": $cyan,
  );

  @each $name, $color in $colors {
    @debug ("#{$name}: '#{$color}'");
    @debug ("#{$name}-100: '#{tint-color($color, 8)}'");
    @debug ("#{$name}-200: '#{tint-color($color, 6)}'");
    @debug ("#{$name}-300: '#{tint-color($color, 4)}'");
    @debug ("#{$name}-400: '#{tint-color($color, 2)}'");
    @debug ("#{$name}-500: '#{$color}'");
    @debug ("#{$name}-600: '#{shade-color($color, 2)}'");
    @debug ("#{$name}-700: '#{shade-color($color, 4)}'");
    @debug ("#{$name}-800: '#{shade-color($color, 6)}'");
    @debug ("#{$name}-900: '#{shade-color($color, 8)}'");
  }

  @debug ("gray: '#{$gray-500}'");
  @debug ("gray-100: '#{$gray-100}'");
  @debug ("gray-200: '#{$gray-200}'");
  @debug ("gray-300: '#{$gray-300}'");
  @debug ("gray-400: '#{$gray-400}'");
  @debug ("gray-500: '#{$gray-500}'");
  @debug ("gray-600: '#{$gray-600}'");
  @debug ("gray-700: '#{$gray-700}'");
  @debug ("gray-800: '#{$gray-800}'");
  @debug ("gray-900: '#{$gray-900}'");

  @debug ("border-color: '#{$border-color}'");
  @debug ("text-secondary: '#{$text-secondary}'");

  @each $name, $color in $social-colors {
    @debug ("#{$name}: '#{$color}'");
  }
}
