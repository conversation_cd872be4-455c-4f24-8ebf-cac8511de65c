@use "sass:color";

//
// Dark mode
//
$darken-dark: color.adjust($dark, $lightness: -2%) !default;
$lighten-dark: color.adjust($dark, $lightness: 2%) !default;
$border-color-dark: color.adjust($dark, $lightness: 8%) !default;
$border-color-translucent-dark: rgba(72, 110, 149, .14) !default;
$border-dark-color-dark: color.adjust($dark, $lightness: 4%) !default;
$border-active-color-dark: color.adjust($dark, $lightness: 12%) !default;

//new bootsrap variables
$body-color-dark: $gray-200 !default; 
$body-emphasis-color-dark: $white !default;

$code-color-dark: var(--#{$prefix}gray-300) !default;
$text-secondary-dark: rgba(153, 159, 164, 1) !default;
 