.loader {
  position: relative;
  display: block;
  width: $loader-size;
  height: $loader-size;
  color: $blue;
  vertical-align: middle;

  &:after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: "";
    border: 1px var(--#{$prefix}border-style);
    border-color: transparent;
    border-top-color: currentColor;
    border-left-color: currentColor;

    border-radius: $border-radius-pill;
    animation: rotate-360 .6s linear;
    animation-iteration-count: infinite;
  }
}

/**
Dimmer
*/
.dimmer {
  position: relative;

  .loader {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    display: none;
    margin: 0 auto;
    transform: translateY(-50%);
  }

  &.active {
    .loader {
      display: block;
    }

    .dimmer-content {
      pointer-events: none;
      opacity: .1;
    }
  }
}


@keyframes animated-dots {
  0% {
    transform: translateX(-100%);
  }
}

.animated-dots {
  display: inline-block;
  overflow: hidden;
  vertical-align: bottom;

  &:after {
    display: inline-block;
    content: "...";
    animation: animated-dots 1.2s steps(4, jump-none) infinite;
  }
}