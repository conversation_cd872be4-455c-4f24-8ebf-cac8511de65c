.table {
  thead {
    th {
      background: $table-th-bg;
      @include subheader;
      padding-top: $table-th-padding-y;
      padding-bottom: $table-th-padding-y;
      white-space: nowrap;

      @media print {
        background: transparent;
      }
    }
  }
}

.table-responsive {
  .table {
    margin-bottom: 0;
  }

  + .card-footer {
    border-top: 0;
  }
}

.table-transparent {
  thead {
    th {
      background: transparent;
    }
  }
}

.table-nowrap {
  > :not(caption) > * > * {
    white-space: nowrap;
  }
}

.table-vcenter {
  > :not(caption) > * > * {
    vertical-align: middle;
  }
}

.table-center {
  > :not(caption) > * > * {
    text-align: center;
  }
}

.td-truncate {
  max-width: 1px;
  width: 100%;
}

.table-mobile {
  @each $breakpoint, $breakpoint-max-widthin in $grid-breakpoints {
    &#{breakpoint-infix($breakpoint)} {
      @include media-breakpoint-down($breakpoint) {
        display: block;

        thead {
          display: none;
        }

        tbody,
        tr {
          display: flex;
          flex-direction: column;
        }

        td {
          display: block;
          padding: $table-cell-padding-x $table-cell-padding-y !important;
          border: none;
          color: var(--#{$prefix}body-color) !important;

          &[data-label] {
            &:before {
              @include subheader;
              content: attr(data-label);
              display: block;
            }
          }
        }

        tr {
          border-bottom: var(--#{$prefix}border-width)
            var(--#{$prefix}border-style) $table-border-color;
        }

        .btn {
          display: block;
        }
      }
    }
  }
}

/**
Table sort
 */
.table-sort {
  font: inherit;
  color: inherit;
  text-transform: inherit;
  letter-spacing: inherit;
  border: 0;
  background: inherit;
  display: block;
  width: 100%;
  text-align: inherit;
  @include transition(color $transition-time);

  margin: (-$table-th-padding-y) (-$table-th-padding-x);
  padding: $table-th-padding-y $table-th-padding-x;

  &:hover,
  &.asc,
  &.desc {
    color: var(--#{$prefix}body-color);
  }

  &:after {
    content: "";
    display: inline-flex;
    width: 1rem;
    height: 1rem;
    vertical-align: bottom;
    mask-image: $table-sort-bg-image;
    background: currentColor;
    margin-left: .25rem;
  }

  &.asc:after {
    mask-image: $table-sort-desc-bg-image;
  }

  &.desc:after {
    mask-image: $table-sort-asc-bg-image;
  }
}

.table-borderless {
  thead th {
    background: transparent;
  }
}

//
// Table selectable
//
.table-selectable {
  tbody tr {
    .on-checked {
      display: none;
    }

    .on-unchecked {
      display: initial;
    }

    &:has(.table-selectable-check:checked) {
      background-color: $table-active-bg;

      .on-checked {
        display: initial;
      }

      .on-unchecked {
        display: none;
      }
    }
  }
}