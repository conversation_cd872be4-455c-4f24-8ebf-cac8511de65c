#seo_wrap {
  position: relative;
}
#seo_wrap .seo-preview * {
  word-break: break-all;
}
#seo_wrap .seo-preview .page-title-seo {
  font-size: 18px;
  color: #1a0dab;
  margin-bottom: 2px;
  font-weight: 400;
}
#seo_wrap .seo-preview .page-title-seo p {
  margin-bottom: 0;
}
#seo_wrap .seo-preview .page-index-status {
  display: inline-block;
  font-size: 14px;
  color: var(--bb-warning);
}
#seo_wrap .seo-preview .page-description-seo p {
  display: block;
  color: #545454;
  line-height: 18px;
  font-size: 13px;
}
#seo_wrap .seo-preview .page-url-seo p {
  display: block;
  word-wrap: break-word;
  color: #006621;
  font-size: 13px;
  line-height: 16px;
  margin-bottom: 2px;
}
#seo_wrap .seo-preview.noindex {
  text-decoration-line: line-through;
  color: var(--bb-secondary-color) !important;
}
#seo_wrap .seo-preview.noindex .page-title-seo,
#seo_wrap .seo-preview.noindex .page-url-seo p {
  text-decoration-line: line-through;
  color: var(--bb-secondary-color) !important;
}

[data-bs-theme=dark] #seo_wrap .seo-preview .page-title-seo {
  color: #8ab4f8;
}
[data-bs-theme=dark] #seo_wrap .seo-preview .page-url-seo p {
  color: #74cd91;
}
