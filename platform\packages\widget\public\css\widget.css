.widget-main li {
  list-style: none;
}
.widget-main .widget-item:hover {
  cursor: pointer;
}
.widget-main .widget-item .card-no-border-bottom-radius {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.widget-main .widget-item .widget-content {
  display: none;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-top: none;
}
.widget-main .widget-item .widget-content form .form-group:not(:nth-child(2)) {
  margin-top: 8px;
}
.widget-main .sidebar-item .card-body .widget-description {
  display: none;
}
.widget-main .sidebar-item .card-body:has(.sortable-ghost) .dropzone {
  display: none;
}
.widget-main .sidebar-item .card-body .sortable-ghost ~ .dropzone,
.widget-main .sidebar-item .card-body .sortable-ghost + .dropzone {
  display: none;
}
.widget-main .sidebar-item .card-body .sortable-ghost .btn-action i {
  display: block !important;
}
.widget-main .ts-control .item {
  width: calc(100% - 15px) !important;
}
