@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,200;0,300;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,700;1,800&family=Roboto:ital,wght@0,400;0,500;0,700;0,900;1,400;1,500;1,700;1,900&display=swap");
body {
  font-family: var(--primary-font), sans-serif;
  font-size: 15px;
  font-weight: normal;
  color: #777;
  font-style: normal;
  line-height: 26px;
}

.pb-380 {
  padding-bottom: 380px;
}

.circle-left {
  position: relative;
  top: -4px;
  margin-inline-end: 2px;
}

.text-left {
  text-align: left !important;
}

.mean-container a.meanmenu-reveal span {
  background: no-repeat;
  display: block;
  height: 3px;
  margin-top: 3px;
  border-top: 3px solid #fff;
}

.mean-container .mean-nav ul li a {
  border-top: 1px solid rgba(217, 217, 217, 0.5);
  color: #333;
  display: block;
  float: left;
  margin: 0;
  padding: 10px 5%;
  text-align: start;
  text-decoration: none;
  text-transform: uppercase;
  width: 90%;
  font-size: 15px;
  font-weight: 600;
}

.mean-container .mean-nav {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.1019607843);
  position: absolute;
}

.mean-container .mean-nav ul {
  overflow: hidden;
}

.mean-container .mean-nav ul li a.mean-expand {
  text-align: right;
  top: 0;
  width: 100%;
  z-index: 2;
  padding: 9px 12px;
}

.mean-container .mean-nav ul li a.mean-expand:hover {
  background: rgba(0, 0, 0, 0);
}

.srb-line {
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: -90px;
}

.line5 {
  display: block;
}

.mr-15 {
  margin-inline-end: 15px;
}

.img {
  max-width: 100%;
  transition: all 0.3s ease-out 0s;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.fix {
  overflow: hidden;
}

a,
.button {
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover,
.portfolio-cat a:hover,
.footer -menu li a:hover {
  text-decoration: none;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: none;
}

a,
button {
  color: #101010;
  outline: medium none;
}

a:hover {
  color: var(--primary-color);
}

.box20 .title a {
  color: #fff;
}

.box20 .title a:hover {
  color: #444;
}

button:focus,
input:focus,
input:focus,
textarea,
textarea:focus {
  outline: none;
  box-shadow: none;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--heading-font), sans-serif;
  color: #101010;
  margin-top: 0;
  font-style: normal;
  font-weight: 600;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
}

h1 {
  font-size: 40px;
  font-weight: 600;
}

h2 {
  font-size: 35px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

ul {
  margin: 0px;
  padding: 0px;
}

li {
  list-style: none;
}

p {
  margin-bottom: 15px;
}

hr {
  border-bottom: 1px solid #eceff8;
  border-top: 0 none;
  margin: 30px 0;
  padding: 0;
}

label {
  color: #7e7e7e;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}

*::-moz-selection {
  background: #d6b161;
  color: #fff;
  text-shadow: none;
}

::-moz-selection {
  background: #444;
  color: #fff;
  text-shadow: none;
}

::selection {
  background: #444;
  color: #fff;
  text-shadow: none;
}

*::-moz-placeholder {
  color: #555555;
  font-size: 14px;
  opacity: 1;
}

*::placeholder {
  color: #555555;
  font-size: 14px;
  opacity: 1;
}

.theme-overlay {
  position: relative;
}

.theme-overlay::before {
  background: #1696e7 none repeat scroll 0 0;
  content: "";
  height: 100%;
  inset-inline-start: 0;
  opacity: 0.6;
  position: absolute;
  top: 0;
  width: 100%;
}

.separator {
  border-top: 1px solid #f2f2f2;
}

/* button style */
.btn {
  color: #fff;
  display: inline-block;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 1;
  margin-bottom: 0;
  padding: 20px 30px;
  text-align: center;
  text-transform: unset;
  touch-action: manipulation;
  transition: all 0.3s ease 0s;
  vertical-align: middle;
  white-space: nowrap;
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  background: var(--primary-color);
  border: none;
  border-radius: 0;
}

.btn i {
  margin-inline-start: 10px;
  font-size: 18px;
}

.btn::before {
  width: 40px;
  height: 40px;
  position: absolute;
  border-radius: 50%;
  content: "";
  background: var(--primary-color);
  top: 10px;
  inset-inline-start: 10px;
  z-index: -1;
  transition: all 0.3s ease 0s;
  display: none;
}

.btn:hover {
  background: var(--primary-color-hover);
  color: #fff;
}

.btn:not(.button-loading):hover::before {
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 4px;
  content: "";
  background: var(--primary-color);
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.btn.button-loading:hover::before {
  background-color: var(--primary-color-hover) !important;
}

.slider-btn:hover .btn-after {
  margin-top: -50px;
  margin-inline-start: 0px;
}

/* 2. header */
.header-top {
  padding-top: 40px;
  border-bottom: 1px solid #e6e6e6;
  padding-bottom: 30px;
}

.innder-ht {
  padding: 10px 0;
}

.wellcome-text p {
  margin-bottom: 0;
  color: #8a8a8a;
}

.header-cta ul li {
  display: inline-block;
  padding-inline-end: 30px;
}

.header-cta ul li:first-child {
  margin-inline-start: 0;
}

.header-cta ul li:last-child {
  padding-inline-end: 0;
  border-inline-end: none;
}

.header-cta ul li i {
  display: inline-block;
  margin-inline-end: 5px;
  position: relative;
  top: 1px;
  color: #fff;
}

.header-top-cta ul li {
  display: inline-block;
  width: 200px;
  margin-inline-end: 80px;
}

.header-top-cta ul li:last-child {
  margin-inline-end: 0;
}

.h-cta-icon {
  float: left;
  display: block;
  margin-inline-end: 20px;
}

.h-cta-content {
  overflow: hidden;
}

.h-cta-content h5 {
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0;
}

.h-cta-content p {
  margin-bottom: 0;
  line-height: 1.5;
}

.menu-area {
  position: relative;
}

.menu .sub-menu {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 250px;
  z-index: 1;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
  border-top: 4px solid #faa292;
  box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
}

.menu .sub-menu,
.menu .children {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 250px;
  z-index: 1;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
  border-top: 4px solid var(--primary-color);
  box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
}

.main-menu {
  position: relative;
  z-index: 9;
}

.menu .sub-menu li {
  margin-inline-start: 0 !important;
  float: left;
  border-bottom: 1px solid #ddd;
  width: 100%;
  text-align: start;
}

.main-menu .sub-menu li a {
  padding: 15px;
}

.menu .sub-menu li,
.menu .children li {
  margin-inline-start: 0 !important;
  float: left;
  border-bottom: 1px solid #ddd;
  width: 100%;
  text-align: start;
}

.menu .menu-item-has-children:hover > ul {
  display: block;
  margin-top: 0px;
}

.main-menu .menu-item-has-children > ul > .menu-item-has-children > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: -255px;
  margin-top: 0;
  top: -3px;
}

.menu .menu-item-has-children:hover > ul,
.menu .page_item_has_children:hover > ul {
  display: block;
  margin-top: 0px;
}

.menu .page_item_has_children > ul > .page_item_has_children:hover > ul {
  margin-inline-start: -265px;
  margin-top: 0;
  top: -3px;
}

.main-menu .menu-item-has-children > ul > .menu-item-has-children:hover > ul,
.menu .page_item_has_children > ul > .page_item_has_children:hover > ul {
  display: block;
}

.main-menu .menu-item-has-children > ul > .menu-item-has-children:hover > ul {
  display: block;
}

.main-menu .menu-item-has-children > ul > .menu-item-has-children > ul > .menu-item-has-children > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: 255px;
  margin-top: 0;
  top: -3px;
}

.main-menu ul li {
  display: inline-block;
  margin-inline-start: 25px;
  position: relative;
}

.main-menu ul li.active a {
  color: var(--primary-color);
}

.main-menu ul li a {
  display: block;
  color: #101010;
  padding: 20px 0;
  font-weight: 400;
  position: relative;
  font-size: 16px;
  font-family: var(--primary-font), sans-serif;
  text-transform: capitalize;
  letter-spacing: 1.5px;
}

.main-menu ul li a::after {
  content: "+";
  margin-inline-start: 10px;
  color: var(--primary-color);
  display: none;
}

.main-menu ul li:last-child a::after,
.main-menu .has-sub ul li a::after {
  display: none;
}

.main-menu ul li:first-child {
  margin-inline-start: 0;
}

.menu-area .menu-tigger {
  cursor: pointer;
  display: inline-block;
}

.menu-area .menu-tigger span {
  height: 2px;
  width: 30px;
  background: #2935bb;
  display: block;
  margin: 7px 0;
  transition: 0.3s;
}

.main-menu ul li:hover > a {
  color: var(--primary-color);
}

.main-menu ul li:hover > a::before {
  width: 100%;
}

.display-ib {
  display: inline-block;
}

.header-social a {
  font-size: 14px;
  display: inline-block;
  margin-inline-start: 15px;
  text-align: center;
  color: #191d3b;
}

.search-top {
  display: inline-block;
}

.search-top li a {
  color: var(--primary-color);
  float: left;
  background: var(--secondary-color);
  padding: 10px 20px;
  margin-top: 6px;
  font-size: 20px;
}

.search-top li:last-child {
  border: none;
}

.header-social a:hover {
  color: #fff;
}

.sticky-menu {
  left: 0;
  margin: auto;
  position: fixed !important;
  top: 0;
  width: 100%;
  box-shadow: 0 0 60px 0 rgba(0, 0, 0, 0.07);
  z-index: 5 !important;
  background: #fff;
  -webkit-animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
  animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
}

.responsive {
  display: none;
}

#mobile-menu {
  display: block;
}

.right-menu li {
  float: right;
  padding: 10px 20px 10px 0;
}

.right-menu li:last-child {
  padding-inline-end: 0;
}

.right-menu .icon {
  float: left;
  margin-inline-end: 20px;
}

.right-menu .text {
  float: left;
  padding-top: 5px;
}

.right-menu .text span {
  display: block;
  color: #517496;
}

.right-menu .text strong {
  color: #fff;
  font-size: 22px;
  font-weight: 900;
  font-family: var(--primary-font), sans-serif;
}

.menu-search {
  padding: 10px 22px 10px 0;
  border-inline-end: 1px solid #e7e7e7;
}

.menu-search a {
  font-size: 16px;
  display: inline-block;
  margin-top: 5px;
  background: rgba(255, 255, 255, 0.2901960784);
  text-align: center;
  line-height: 33px;
  border-radius: 50%;
  color: var(--primary-color);
}

.top-btn {
  background: var(--secondary-color);
  padding: 16px 30px;
  width: 100%;
  float: left;
  color: #fff;
  text-align: center;
  text-transform: uppercase;
  border: 2px solid transparent;
  transition: background-color 0.3s ease;
}

.top-btn i {
  font-size: 16px;
  margin-inline-start: 10px;
}

.top-btn:hover {
  background-color: var(--primary-color);
  border: 2px solid var(--secondary-color);
  color: #fff;
}

.sticky-menu .logo {
  margin-top: 0;
  line-height: 61px;
  height: auto;
}

.slider-content {
  position: relative;
  z-index: 2;
}

.slider-bg .banner-img {
  overflow: hidden;
  height: 738px;
  margin-top: 115px;
  margin-inline-end: -62px;
}

.slider-content.s-slider-content.text2 {
  margin-top: 0;
}

.slider-content.s-slider-content.text3 {
  margin-top: 168px;
}

.slider-price {
  position: absolute;
  inset-inline-end: 0;
  bottom: 0;
}

.slider-price h3 {
  color: #fff;
}

.slider-price h2 {
  color: var(--primary-color);
  font-size: 60px;
  font-weight: 500;
}

.slider-btn {
  display: inline-block;
  position: relative;
}

.slider-active .slick-dots {
  position: absolute;
  top: 40%;
  left: 22%;
}

.slider-active .slick-dots li button {
  text-indent: -99999px;
  border: none;
  padding: 0;
  height: 1px;
  margin-inline-start: 10px;
  background: var(--primary-color);
  border-radius: 50px;
  z-index: 1;
  cursor: pointer;
  transition: 0.3s;
}

.slider-active .slick-dots li.slick-active button {
  width: 50px;
  background: var(--primary-color);
}

.slider-active .slick-dots li.slick-active button::before {
  opacity: 1;
}

.slider-active .slick-dots li button::before {
  content: "";
  width: 19px;
  height: 19px;
  float: left;
  position: relative;
  margin-top: -9px;
  left: -19px;
  border-radius: 50%;
  opacity: 0.7;
  background-repeat: no-repeat;
  background-position: center;
}

.second-header {
  border: none;
  padding-bottom: 0;
  background: #101010;
  padding-top: 0;
  color: #fff;
  position: relative;
  z-index: 1;
}

.second-header a,
.second-header span {
  color: #fff;
}

.second-header a:hover {
  color: var(--secondary-color);
}

.second-menu {
  position: relative;
  z-index: 9;
}

.sticky-menu .second-menu {
  margin: 0;
  box-shadow: none;
  padding: 0;
}

.sticky-menu .second-menu::before {
  content: none;
}

.second-menu .main-menu ul li {
  margin-inline-start: 40px;
}

.second-menu .main-menu ul li:first-child {
  margin-inline-start: 0;
}

.second-menu .main-menu ul li a::before {
  content: none;
}

.second-header-btn .btn {
  background: var(--primary-color);
  box-shadow: 0px 10px 30px 0px rgba(54, 96, 217, 0.3);
  font-size: 16px;
  text-transform: uppercase;
  color: #fff;
  padding: 20px 30px;
  position: relative;
  border-radius: 4px;
}

.second-header-btn .btn:hover {
  color: #fff;
  background: var(--primary-color);
}

.img-main {
  position: relative;
  z-index: 2;
}

.offcanvas-menu {
  position: fixed;
  inset-inline-end: 0;
  height: 100%;
  width: 300px;
  z-index: 999;
  background: #00081b;
  top: 0;
  padding: 30px;
  transition: 0.5s;
  transform: translateX(100%);
}

.offcanvas-menu.active {
  transform: translateX(0);
}

.menu-close i {
  font-size: 18px;
  color: #fff;
  transition: 0.3s;
  cursor: pointer;
}

.menu-close:hover i {
  color: var(--primary-color);
}

.offcanvas-menu ul {
  margin-top: 30px;
}

.offcanvas-menu ul li {
  border-bottom: 1px solid #101c38;
}

.offcanvas-menu ul li a {
  color: #fff;
  font-size: 18px;
  text-transform: capitalize;
  padding: 6px 0;
  display: block;
}

.offcanvas-menu ul li:hover a {
  color: var(--primary-color);
}

.side-social a {
  color: #fff;
  margin-inline-end: 10px;
}

.side-social {
  margin-top: 30px;
}

.side-social a:hover {
  color: var(--primary-color);
}

.offcanvas-menu form {
  position: relative;
  margin-top: 30px;
}

.offcanvas-menu form input {
  width: 100%;
  background: none;
  border: 1px solid #2d3547;
  padding: 7px 10px;
  color: #fff;
}

.offcanvas-menu form button {
  position: absolute;
  border: none;
  inset-inline-end: 0;
  background: var(--primary-color);
  padding: 8px 14px;
  top: 0;
  cursor: pointer;
}

.offcanvas-menu form button i {
  color: #fff;
}

.offcanvas-overly {
  position: fixed;
  background: #000;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
}

.offcanvas-overly.active {
  opacity: 0.5;
  visibility: visible;
}

.off-logo {
  display: none;
}

.header-two .menu-area {
  padding-top: 0;
}

.header-two .top-callus {
  background: var(--primary-color);
  color: #fff;
  display: inline-block;
  padding: 15px 30px;
  border-radius: 0 0px 10px 10px;
  width: 100%;
}

.header-two #mobile-menu {
  float: left;
}

.header-two .logo img {
  width: auto;
  max-width: 60%;
}

.header-two .main-menu ul li a {
  color: var(--primary-color);
}

.header-three .second-header {
  background: var(--primary-color);
  padding: 10px 0;
}

.header-three .menu-area::before {
  display: none;
}

.header-three .menu-area {
  margin-bottom: -110px;
  border-top: 0;
  position: relative;
  z-index: 9;
  background: rgba(9, 9, 9, 0.4588235294);
}

.header-three .sticky-menu {
  margin-top: 0;
  background: var(--primary-color);
}

.header-three .main-menu ul li a {
  font-family: var(--primary-font), sans-serif;
  color: #fff;
}

.header-three .main-menu .sub-menu li a,
.menu .children li a {
  color: #4f4f4f !important;
}

.header-three .main-menu .sub-menu li a:hover,
.menu .children li a:hover {
  color: var(--primary-color) !important;
}

.header-three .btn.ss-btn {
  float: right;
}

.login li {
  display: inline-block;
}

.login li a {
  color: #fff;
  text-transform: uppercase;
}

.slider-three .slider-img {
  margin-inline-start: -25px;
  margin-top: 50px;
  margin-inline-end: -227px;
}

.slider-bg-three::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(4, 15, 45, 0.568627451);
  z-index: 1;
}

.slider-four .slider-bg h5 {
  border: none;
  color: rgba(255, 255, 255, 0.1098039216);
  font-size: 170px;
}

.slider-four .slider-img {
  margin-inline-start: 0;
  margin-top: 136px;
  margin-inline-end: -235px;
  float: right;
}

.slider-four .slider-img img {
  position: relative;
  z-index: 1;
}

.slider-four .slider-bg .text2 h5 {
  color: var(--primary-color);
  font-size: 18px;
  font-family: var(--heading-font), sans-serif;
  font-weight: 500;
}

.slider-four .slider-bg .video-i i {
  border: none;
  color: var(--primary-color);
  margin-inline-end: 10px;
}

.slider-four .slider-bg .text2 p {
  color: #777;
}

.single-slider .subricbe {
  border-radius: 4px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 20px 60px 0px rgba(0, 0, 0, 0.08);
  position: relative;
  padding: 15px;
  margin-top: 40px;
  width: 92%;
}

.header-input {
  padding: 0 1rem;
  width: 100%;
  height: 4.4rem;
  outline: none !important;
  margin-bottom: 0;
  border: 1px solid #f8f8f8;
  background: #f8f8f8;
  border-radius: 0;
}

.contact-form .btn::before {
  display: none;
}

.header-btn {
  position: absolute !important;
  inset-inline-end: 15px;
  top: 15px;
  bottom: 15px;
  border-radius: 0;
  background: var(--primary-color);
  border: none;
  color: #fff;
  padding: 5px 15px;
}

.header-btn i {
  margin-inline-start: 0;
}

.search-top2 {
  float: right;
}

.search-top2 li {
  margin-inline-start: 20px;
  float: left;
  border-radius: 10px;
  background: var(--primary-color);
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 45px;
}

.search-top2 li a {
  float: none !important;
}

.search-top2 li .fas.fa-search {
  color: #36383a;
}

.header-slidemenu {
  top: 0px;
  left: 0px;
  bottom: 0px;
  height: 100%;
  position: fixed;
  padding-top: 80px;
  padding-inline-start: 70px;
  z-index: 2;
  background: var(--primary-color);
  width: 16.7%;
}

.slide-out ul li {
  display: inline-block;
  margin-inline-start: 0;
  width: 75%;
}

.slide-out .has-sub > ul {
  opacity: 0;
  position: absolute;
  background-color: white;
  min-width: 250px;
  z-index: 1;
  transition: all 0.3s ease-in-out;
  margin-top: -45px;
  box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  visibility: hidden;
  margin-inline-start: 130px;
}

.slide-out .has-sub:hover > ul {
  opacity: 1;
  margin-top: -45px;
  visibility: visible;
  top: 0;
}

.slide-out .has-sub > ul > .has-sub > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: 255px;
  margin-top: 0;
  top: -3px;
}

.slide-out .has-sub > ul > .has-sub > ul > .has-sub > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: 260px;
  margin-top: 0;
  top: -3px;
}

.slide-out ul li a {
  display: block;
  color: #fff;
  font-weight: 400;
  padding: 0px 0 15px;
  position: relative;
  text-transform: uppercase;
}

.slide-out ul li a:hover,
.slide-out ul li:hover > a {
  color: #c9a17b;
}

.slide-out .menu-item-has-children > ul {
  display: none;
  margin-top: -40px;
  margin-inline-start: 120px;
}

.slide-out .menu-item-has-children:hover > ul {
  display: block;
  margin-top: -40px;
  margin-inline-start: 120px;
}

.header-slidemenu .footer-social {
  position: absolute;
  bottom: 50px;
  left: 60px;
}

.overlay-bg-01 {
  position: absolute;
  left: 0;
  top: 0;
}

.overlay-bg-02 {
  position: absolute;
  left: 0;
  bottom: 0;
}

.overlay-bg-03 {
  position: absolute;
  right: 0;
  top: 30px;
}

.overlay-bg-04 {
  position: absolute;
  right: 0;
  bottom: 100px;
}

.slider-bg {
  min-height: 1000px !important;
  background-position: center bottom;
  background-size: cover;
  position: relative;
  z-index: 1;
}

.slider-bg .image-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover !important;
  transform: scale(1);
  transition: transform 7000ms ease;
  z-index: 1;
}

.slider-bg.slick-active .image-layer {
  transform: scale(1.15);
}

.slider-bg .video-i.popup-video {
  text-align: center;
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 16px;
  color: #fff;
  font-family: var(--primary-font), sans-serif;
}

.slider-bg .video-i.popup-video img {
  display: inline-block;
}

.show-bg3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: #000;
  opacity: 0.7;
  z-index: 1;
}

.slider-bg2 {
  min-height: 900px;
  background-position: center;
  background-size: cover;
  position: relative;
  margin-top: -118px;
  z-index: 1;
}

.slider-bg2 .slider-content > span::before {
  content: "";
  background: #383838;
  height: 3px;
  text-align: start;
  width: 60px;
  margin-top: 15px;
  margin-inline-end: 10px;
}

.slider-bg2 .slider-content h2 {
  color: #fff;
}

.slider-bg2 .slider-content p {
  color: #676f67;
  font-size: 16px;
  margin-bottom: 0;
}

.slider-bg2 .slider-content > span {
  font-size: 22px;
  font-weight: 500;
  color: var(--primary-color);
  display: block;
  margin-bottom: 20px;
  margin-top: 30px;
}

.slider-bg .video-i i {
  background: #fff;
  padding: 14px;
  border-radius: 100%;
  width: 60px;
  display: inline-block;
  text-align: center;
  color: var(--primary-color);
  height: 60px;
  line-height: 30px;
  box-shadow: 1px 1.732px 60px 0px rgba(250, 63, 108, 0.1);
  margin-inline-end: 10px;
}

.slider-bg2 .video-i {
  background: #383838;
  padding: 14px;
  border-radius: 100%;
  width: 51px;
  display: inline-block;
  text-align: center;
  color: #fff;
  margin-inline-start: 20px;
}

.slider-bg2 .video-i:hover {
  background: var(--primary-color);
  padding: 14px;
  border-radius: 100%;
  width: 51px;
  display: inline-block;
  text-align: center;
  color: #fff;
  margin-inline-start: 20px;
}

#particles-js {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.slider-content > span {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 6px;
  display: block;
  margin-bottom: 20px;
}

.slider-content h2 {
  font-size: 90px;
  letter-spacing: 1px;
  line-height: 1.2;
  margin-bottom: 40px;
  color: #fff;
}

.slider-content h2 span {
  color: var(--primary-color);
}

.slider-content h5 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 22px;
  text-transform: uppercase;
  line-height: 18px;
}

.slider-content p span {
  display: inline-block;
  height: 2px;
  width: 40px;
  background: var(--primary-color);
  margin-inline-end: 20px;
  position: relative;
  top: -4px;
}

.slider-content p {
  color: #fff;
  font-size: 18px;
  margin-bottom: 0;
}

.p-relative {
  position: relative;
}

.down-arrow {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  color: #fff;
  z-index: 1;
  height: 60px;
  width: 30px;
  margin: auto;
  text-align: center;
  line-height: 60px;
  border: 2px solid #fff;
  border-radius: 50px;
  font-size: 18px;
}

.down-arrow:hover {
  color: #fff;
}

.slider-active .slick-arrow {
  position: absolute;
  top: 45%;
  transform: translateY(-50%);
  left: 5%;
  color: #00163b;
  font-size: 26px;
  line-height: 60px;
  border: none;
  text-align: center;
  z-index: 9;
  cursor: pointer;
  padding: 0;
  background: none;
  transition: 0.5s;
  width: 60px;
  height: 60px;
  background: #fff;
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.slider-active .slick-next {
  right: 5%;
  left: inherit;
}

.slider-active .slick-arrow, .slider-active .slick-next {
  transition: all 0.3s ease 0s;
  opacity: 0;
}

.slider-active:hover .slick-prev {
  left: 4%;
}

.slider-active:hover .slick-next {
  right: 4%;
}

.slider-active:hover .slick-arrow, .slider-active:hover .slick-next {
  opacity: 1;
}

.slider-active .slick-arrow:hover {
  color: #fff;
  background: var(--primary-color);
}

.second-slider-content h2 {
  font-size: 90px;
  margin-bottom: 20px;
}

.second-slider-bg::before {
  opacity: 0.5;
}

.second-slider-content {
  padding-top: 85px;
  padding-bottom: 5px;
}

.s-slider-content h2 {
  margin-bottom: 25px;
}

.s-slider-content p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7490196078);
  line-height: 26px;
  margin: auto;
}

.slider-four .s-slider-content p {
  font-size: 18px;
  color: #fff;
  line-height: 26px;
  padding-bottom: 30px;
  width: 90%;
  margin: auto;
}

.btn-icon {
  position: absolute;
  left: 10px;
  top: 10px;
  bottom: 10px;
  background: #ffb3a5;
  padding: 10px 15px;
  color: #fff;
}

.btn.ss-btn.active {
  background: none;
  color: #fff;
  border: 2px solid #fff;
  box-shadow: none;
}

.line {
  text-align: start;
  margin-inline-end: 15px;
}

.slider-shape {
  position: absolute !important;
  z-index: 9;
}

.ss-one {
  top: 180px !important;
  left: 234px !important;
}

.ss-two {
  top: 238px !important;
  left: 46% !important;
}

.ss-three {
  top: 77% !important;
  left: 12% !important;
}

.ss-four {
  top: 85% !important;
  left: 30% !important;
}

.ss-five {
  left: 61% !important;
  top: 83% !important;
}

.ss-six {
  left: 88% !important;
  top: 80% !important;
}

.ss-seven {
  top: 20% !important;
  left: 89% !important;
}

.slider-bg .text2 h2 {
  font-size: 60px;
}

.slider-bg .text2 a {
  color: #FFF;
}

.slider-bg .text2 .btn.ss-btn.active {
  background: var(--primary-color);
  border: 2px solid var(--primary-color);
  color: #FFF;
}

.slider-bg .text2 p {
  padding-inline-end: 22%;
  color: #fff;
}

.slider-text-2 h2 {
  color: #fff;
}

.s-aliment-1 {
  position: absolute;
  background-color: #ffffff;
  border-radius: 3px;
  padding: 15px;
  box-shadow: 0px 30px 60px 0px rgba(0, 0, 0, 0.2);
  top: 361px;
  right: -122px;
  display: flex;
  align-items: center;
  z-index: 1;
  animation: movedelement 5s linear infinite;
  width: 250px;
}

.aliment-icon-red {
  display: flex;
  align-items: center;
  justify-content: center;
}

.aliment-content {
  margin-inline-start: 15px;
  padding-top: 15px;
}

.aliment-content p {
  margin-bottom: 0;
}

.aliment-content .h3-title {
  font-size: 15px;
  line-height: 15px;
  font-weight: 700;
  margin-bottom: 5px;
}

@keyframes movedelement {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  25% {
    -webkit-transform: translate(10px, 10px);
    transform: translate(10px, 10px);
  }
  50% {
    -webkit-transform: translate(5px, 5px);
    transform: translate(5px, 5px);
  }
  75% {
    -webkit-transform: translate(10px, -5px);
    transform: translate(10px, -5px);
  }
  to {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
.slider-content2 h2 {
  color: var(--primary-color);
}

.slider-content2 p {
  color: #777777;
}

.slider-content2 h5 {
  color: #777777;
}

/* 5. features */
.features-services-area .container {
  background-color: rgb(255, 255, 255);
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
  padding: 50px 50px 20px;
  margin-top: -180px;
}

.features-services-area .features-icon img {
  height: 57px;
  margin-bottom: 30px;
}

.features-services-area p {
  margin-bottom: 0;
}

.features-content h4 {
  font-size: 24px;
  margin-bottom: 17px;
}

.s-btn {
  font-size: 14px;
  font-weight: 500;
  color: #101010;
  display: inline-block;
  border: 1px solid #ccc;
  padding: 9px 30px;
  border-radius: 50px;
}

.s-btn:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

.section-title h5 {
  color: var(--primary-color);
  text-transform: capitalize;
  font-size: 18px;
  margin-bottom: 10px;
}

.section-title h5 div {
  display: inline-block;
  margin: 0 10px;
}

.section-title h2 {
  font-size: 60px;
  padding-bottom: 0;
  margin-bottom: 0px !important;
  position: relative;
}

.section-title h2::before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: 0;
  height: 4px;
  width: 60px;
  background: var(--primary-color);
  margin: auto;
  transition: 0.3s;
  display: none;
}

.section-title p {
  margin-top: 10px;
  width: 60%;
  display: inline-block;
}

section:hover .section-title h2::before {
  width: 100px;
}

.features-p {
  padding-top: 380px;
}

.features-shape {
  position: absolute;
  z-index: -1;
}

.fshape-one {
  inset-inline-start: 120px;
  top: 25%;
}

.fshape-two {
  inset-inline-start: 15%;
  top: 51%;
}

.fshape-three {
  top: 69%;
  inset-inline-start: 6%;
}

.fshape-four {
  top: 40%;
  inset-inline-start: 89%;
}

.fshape-five {
  top: 71%;
  inset-inline-start: 83%;
}

.about-content3 li::before {
  font-family: "Font Awesome 5 Pro";
  content: "\f00c";
  margin-inline-end: 10px;
  color: var(--primary-color);
}

/* 7. video */
.video-position {
  position: absolute;
  bottom: -170px;
  inset-inline-start: 50px;
}

.video-img {
  position: relative;
  overflow: hidden;
  z-index: 1;
  height: 588px;
}

.video-img img {
  width: 100%;
}

.video-img::before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  opacity: 0.65;
}

.video-img a {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
  font-size: 30px;
  color: #fff;
  height: 80px;
  width: 80px;
  text-align: center;
  background: var(--primary-color);
  border-radius: 50%;
  padding: 25px;
}

.video-img a::before {
  content: "";
  position: absolute;
  inset-inline-start: 50%;
  top: 50%;
  height: 90px;
  width: 90px;
  background: transparent linear-gradient(90deg, var(--primary-color) 0%, var(--primary-color) 100%) 0% 0% no-repeat padding-box;
  z-index: -1;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-border 1500ms ease-out infinite;
}

@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
.left-align h2::before {
  margin: unset;
}

.video-content p {
  margin-bottom: 30px;
}

.v-list ul li {
  margin-bottom: 20px;
}

.v-list ul li:last-child {
  margin-bottom: 0px;
}

.v-list ul li i {
  color: var(--primary-color);
  display: inline-block;
  margin-inline-end: 5px;
}

.v-padding {
  padding-top: 35px;
}

.video-area h2 {
  color: #fff;
}

.s-video-wrap {
  background-position: center;
  background-size: cover;
  display: flex;
  align-items: center;
}

.s-video-content {
  width: 100%;
  text-align: center;
}

.s-video-content a {
  z-index: 1;
  font-size: 14px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 15px;
  position: relative;
}

.s-video-content a::before {
  content: "";
  position: absolute;
  inset-inline-start: 50%;
  top: 50%;
  height: 120px;
  width: 120px;
  background: rgba(255, 255, 255, 0.09);
  z-index: -1;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-border 1500ms ease-out infinite;
  display: none;
}

.s-video-content h2 {
  font-size: 60px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 20px;
}

.s-video-content p {
  font-size: 18px;
  color: #fff;
  margin-bottom: 0;
}

/* 8. choose */
.chosse-img {
  background-size: cover;
  height: 100%;
  inset-inline-end: 0;
  position: absolute;
  top: 0;
  width: 50%;
  background-repeat: no-repeat;
  background-position: center;
}

.choose-list {
  float: left;
  width: 100%;
}

.choose-list ul li {
  margin-bottom: 20px;
  color: #444d69;
  width: 50%;
  float: left;
}

.choose-list ul li i {
  color: var(--primary-color);
  display: inline-block;
  margin-inline-end: 5px;
  font-size: 18px;
}

.choose-area {
  background-size: cover;
  background-position: center;
}

.choose-content p {
  margin-bottom: 15px;
  color: #444d69;
  padding-inline-end: 50px;
}

.choose-btn a {
  border-radius: 8px;
  background-color: rgb(31, 83, 219);
  box-shadow: 0px 2px 60px 0px rgba(31, 83, 219, 0.3);
  width: 241px;
  display: inline-block;
  color: #fff !important;
  display: flex;
  padding: 10px 25px;
  float: left;
  margin-inline-end: 25px;
  position: relative;
  border: 2px solid #1f53db;
  font-family: var(--primary-font), sans-serif;
}

.choose-btn a .icon {
  padding: 0 20px 0 0;
  line-height: 50px;
  z-index: 99;
}

.choose-btn a .text {
  z-index: 99;
  font-size: 20px;
}

.choose-btn a .text strong {
  font-size: 22px;
  margin-top: 0px;
  display: block;
  font-weight: 500;
}

.choose-btn a.g-btn {
  box-shadow: none;
  width: 262px;
  background: none;
  border: 2px solid #1f53db;
  margin-inline-end: 0;
}

.g-btn .text {
  color: #101010;
}

.g-btn .text strong {
  color: #1f53db;
}

/* 9. brand */
.single-brand {
  text-align: center;
}

.single-brand img {
  display: inline-block;
}

.brand-area2 {
  margin-top: -70px;
  position: relative;
}

/* 10. work-process */
.wp-bg {
  background-position: center;
  background-size: cover;
}

.w-title h2::before {
  background-color: var(--primary-color);
}

.wp-list {
  background: #232c8e;
  box-shadow: 0px 10px 30px 0px rgba(29, 38, 129, 0.48);
  padding: 80px 50px;
  padding-bottom: 75px;
}

.wp-list ul li {
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  margin-bottom: 45px;
}

.wp-list ul li:last-child {
  margin-bottom: 0px;
}

.wp-icon {
  margin-inline-end: 25px;
}

.wp-content h5 {
  color: #fff;
  font-size: 24px;
  margin-bottom: 15px;
}

.wp-content p {
  color: #fff;
  margin-bottom: 0;
}

.wp-tag {
  position: absolute;
  inset-inline-start: 70px;
  top: 90px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

@keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-10px);
    -moz-transform: rotateX(0deg) translateY(-10px);
    -ms-transform: rotateX(0deg) translateY(-10px);
    -o-transform: rotateX(0deg) translateY(-10px);
    transform: rotateX(0deg) translateY(-10px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
@-webkit-keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-10px);
    -moz-transform: rotateX(0deg) translateY(-10px);
    -ms-transform: rotateX(0deg) translateY(-10px);
    -o-transform: rotateX(0deg) translateY(-10px);
    transform: rotateX(0deg) translateY(-10px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
.wp-img {
  margin-inline-end: -60px;
  margin-top: -60px;
  margin-bottom: -15px;
}

.wp-thumb img {
  width: 100%;
}

.inner-wp-icon {
  float: left;
  display: block;
  margin-inline-end: 30px;
}

.inner-wp-c {
  overflow: hidden;
  display: block;
}

.inner-wp-c h5 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
}

.inner-wp-c p {
  margin-bottom: 0;
}

/* 11. team */
.team-area .single-team {
  margin-inline-start: 0;
  margin-inline-end: 0;
}

.single-team {
  margin-inline-start: 15px;
  margin-inline-end: 15px;
  border-width: 1px;
  border-color: rgb(215, 215, 215);
  border-style: solid;
  padding: 0px;
  border-radius: 4px;
}

.team-area .container {
  z-index: 1;
  position: relative;
}

.team-active .slick-dots {
  text-align: center;
  display: none;
}

.team-active .slick-dots li {
  display: inline-block;
  margin: 0 5px;
}

.team-active .slick-dots .slick-active button {
  width: 10px;
  background-color: var(--primary-color);
}

.team-active .slick-dots li button {
  text-indent: -99999px;
  padding: 0;
  margin-inline-start: 10px;
  z-index: 1;
  cursor: pointer;
  transition: 0.3s;
  background-color: #cccccc;
  height: 10px;
  width: 10px;
  border-radius: 2px;
  border: none;
}

.team-active .slick-dots li.slick-active button::before {
  opacity: 1;
}

.team-active .slick-dots li button::before {
  content: "";
  width: 19px;
  height: 19px;
  float: left;
  position: relative;
  margin-top: -9px;
  inset-inline-start: -19px;
  border-radius: 50%;
  opacity: 0.7;
  background-repeat: no-repeat;
  background-position: center;
}

.team-thumb {
  text-align: center;
  display: block;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.team-thumb img {
  display: inline-block !important;
  width: 100%;
  border-radius: 4px;
}

.team-thumb .dropdown {
  position: absolute;
  bottom: 0;
  inset-inline-end: 15px;
}

.team-thumb .dropdown .xbtn {
  width: 50px;
  height: 50px;
  background: var(--primary-color) 0% 0% no-repeat padding-box;
  display: inline-block;
  font-size: 18px;
  line-height: 48px;
  font-family: "Titillium Web", sans-serif;
  color: #fff;
  cursor: pointer;
  border-radius: 50%;
  position: relative;
}

.team-thumb .dropdown .xbtn::before {
  width: 40px;
  height: 40px;
  content: "";
  position: absolute;
  border: 2px dashed rgba(255, 255, 255, 0.3607843137);
  border-radius: 50%;
  inset-inline-start: 5px;
  top: 5px;
}

.team-social {
  text-align: center;
  transition: all 0.3s ease 0s;
  width: 100%;
  margin-top: 10px;
  position: absolute;
  top: -70px;
  opacity: 0;
}

.single-team:hover .team-social {
  top: -80px;
  opacity: 1;
}

.team-social li {
  display: inline;
  margin: 0 3px;
}

.team-thumb .dropdown-menu {
  background: none;
  border: none;
  padding: 0;
  transform: translate3d(0px, -107px, 0px) !important;
}

.team-thumb .social {
  position: absolute;
  top: 0;
}

.team-info {
  padding: 30px 0px;
  transition: 0.3s;
  position: relative;
  text-align: center;
}

.team-info .text {
  padding-inline-start: 30px;
  border-inline-start: 5px solid var(--primary-color);
}

.team-info h4 {
  font-size: 24px;
  margin-bottom: 5px;
  transition: 0.3s;
}

.team-info span {
  font-size: 16px;
  transition: 0.3s;
  color: var(--primary-color);
  font-weight: 600;
}

.team-info p {
  margin-bottom: 0;
  color: var(--primary-color);
}

.team-social a {
  color: #fff;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  font-size: 16px;
  position: relative;
  z-index: 999;
  margin: 5px 0;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  line-height: 40px;
}

.team-social a:hover {
  background: var(--primary-color);
  color: #fff;
}

.single-team:hover .team-thumb .brd::before,
.single-team:hover .team-thumb .brd::after {
  color: var(--primary-color);
}

.single-team:hover .team-info {
  border-color: var(--primary-color);
}

.team-t h2 {
  top: 50px;
}

.single-team:hover .team-info h4 {
  color: var(--primary-color);
}

.slick-slide {
  outline: none;
}

.team-active {
  position: relative;
}

.team-active .slick-arrow {
  position: absolute;
  top: 30%;
  inset-inline-start: -50px;
  border: none;
  background: none;
  padding: 0;
  font-size: 20px;
  color: #fff;
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
  background: var(--primary-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.team-active .slick-arrow:hover {
  background: #12265a;
}

.team-active .slick-next {
  inset-inline-start: inherit;
  inset-inline-end: -50px;
}

.team-area-content {
  padding: 100px 0;
}

.team-info h4 a:hover {
  color: var(--primary-color);
}

.team-area-content span {
  margin-bottom: 10px;
  color: var(--primary-color);
  font-size: 18px;
  display: inline-block;
  font-weight: 500;
  position: relative;
}

.team-area-content span::before {
  content: "";
  width: 50px;
  height: 1px;
  background: var(--primary-color);
  position: relative;
  display: inline-block;
  top: -5px;
  margin-inline-end: 15px;
}

.team-area-content ul {
  margin-bottom: 50px;
}

.team-area-content li {
  display: flex;
  margin-top: 15px;
}

.team-area-content li .icon i {
  margin-inline-end: 10px;
  color: var(--primary-color);
  width: 20px;
}

.team-area-content li .icon strong {
  color: var(--primary-color);
}

.team-area-content li .icon {
  margin-inline-end: 10px;
  width: 35%;
}

.team-area-content .social a {
  font-size: 12px;
  color: #fff;
  margin: 0 5px;
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  line-height: 40px;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.1490196078);
}

.team-area-content .social a:nth-child(1) {
  background: #345aa8;
}

.team-area-content .social a:nth-child(2) {
  background: #00aced;
}

.team-area-content .social a:nth-child(3) {
  background: #0057ff;
}

.team-area-content .social a:nth-child(4) {
  background: #cd201f;
}

.per-info {
  background: #f7f5f1;
  padding: 30px;
  border: 2px solid #f5f5f5;
}
@media (max-width: 991px) {
  .per-info {
    margin-bottom: 50px;
  }
}
.per-info h4 {
  text-align: center;
}

.per-info ul {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
}
@media (max-width: 991px) {
  .per-info ul li {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
  .per-info ul li .icon {
    width: 120px;
  }
}
@media (min-width: 991px) and (max-width: 1200px) {
  .per-info ul li {
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .per-info ul li .icon {
    width: 100%;
    margin-bottom: 10px;
  }
}
@media (min-width: 1200px) {
  .per-info ul li {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
}

.brand-anim-01 {
  position: absolute;
  inset-inline-start: -40px;
  top: -60px;
}

.brand-anim-02 {
  position: absolute;
  inset-inline-end: -36px;
  top: 40px;
}

/* 12. counter */
.counter-tittle {
  display: inline-block;
  background: var(--primary-color);
  padding: 11px 30px;
  position: relative;
  top: -60px;
}

.counter-tittle h2 {
  color: #fff;
  font-size: 25px;
}

.counter-tittle::before {
  content: "";
  background: var(--primary-color);
  width: 17%;
  height: 62px;
  position: absolute;
  top: -2px;
  transform: skew(20deg);
  inset-inline-start: -13px;
}

.counter-tittle::after {
  content: "";
  background: var(--primary-color);
  width: 17%;
  height: 62px;
  position: absolute;
  top: -2px;
  transform: skew(-20deg);
  inset-inline-end: -13px;
}

.counter-area .single-counter {
  border-bottom: 1px solid #ccc;
  padding-bottom: 30px;
  padding-inline-start: 0;
  padding-inline-end: 0;
}

.counter-area .row div:nth-child(4) .single-counter {
  border-inline-end: none;
}

.single-counter {
  padding: 0 25px;
}

.single-counter i {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: #fe576b;
  border-radius: 50%;
  margin-bottom: 30px;
  color: #000;
  font-size: 40px;
  line-height: 80px;
}

.single-counter .icon {
  margin-bottom: 15px;
  float: left;
  width: 100px;
}

.count {
  font-size: 50px;
  color: #101010;
  font-weight: 700;
  margin-bottom: 5px;
  line-height: 1;
  font-family: var(--primary-font), sans-serif;
  display: inline-block;
}

.counter {
  display: flex;
}

.counter small {
  font-size: 35px;
  font-weight: 600;
  color: #002c47;
  line-height: 1;
  position: relative;
}

.single-counter p {
  font-size: 18px;
  margin-bottom: 0;
  color: #939cb3;
  padding-top: 10px;
  padding-inline-start: 30px;
}

.single-counter .line {
  display: inline-block;
  width: 1px;
  height: 50px;
  background: #fe576b;
  margin-bottom: -5px;
  margin-top: 10px;
}

/* 13. cta */
.cta-bg {
  position: relative;
  background-size: cover;
  background-position: center;
  z-index: 1;
}

.cta-title h2 {
  padding-bottom: 0;
  font-size: 62px;
  color: #fff;
}

.cta-title h3 {
  padding-bottom: 0;
  font-size: 18px;
  color: #fff;
  text-transform: uppercase;
}

.cta-title p {
  font-size: 15px;
}

.cta-title h2::before {
  content: none;
}

.cta-content p {
  color: #fff;
  padding-inline-end: 80px;
  margin-bottom: 35px;
}

.cta-btn .btn {
  border: 1px solid var(--primary-color);
}

.cta-btn .btn::before {
  background: var(--primary-color);
}

.cta-right p {
  margin-bottom: 0;
  color: #fff;
  font-size: 24px;
  font-weight: 500;
}

.call-tag {
  margin-bottom: 20px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.cta-bg .video-img {
  height: 757px;
}

.cta-bg .social {
  text-align: center;
}

.cta-bg .social .icon {
  display: inline-block;
  width: 110px;
  height: 110px;
  background: var(--primary-color);
  padding: 30px;
  border-radius: 50%;
  line-height: 47px;
  margin-bottom: 30px;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.4);
}

.cta-bg .line {
  text-align: center;
}

.cta-bg .social a {
  font-size: 22px;
  color: #fff;
  font-weight: 600;
}

.portfolio .button-group {
  padding-bottom: 0;
}

/* 14. testimonial */
.testimonial-area .container {
  z-index: 1;
  position: relative;
}

.testimonial-area .test-bg {
  position: absolute;
  top: 0;
  inset-inline-start: -27px;
}

.testimonial-active2 {
  width: 95%;
  margin: auto;
}

.testimonial-area h2 {
  margin-bottom: 15px !important;
}

.testimonial-area p {
  margin-top: 0;
}

.ta-bg {
  height: 475px;
  width: 475px;
  background-repeat: no-repeat;
  margin-top: 25px;
}

.testimonial-author .ta {
  position: absolute;
  inset-inline-start: 50%;
  top: 50%;
  overflow: hidden;
  transition: all 1200ms ease;
  -moz-transition: all 1200ms ease;
  -webkit-transition: all 1200ms ease;
  -ms-transition: all 1200ms ease;
  -o-transition: all 1200ms ease;
}

.author-one.now-in-view {
  top: -25px;
  inset-inline-start: 110px;
}

.author-two.now-in-view {
  top: 21%;
  inset-inline-start: 69%;
}

.author-three.now-in-view {
  inset-inline-start: 67%;
  top: 66%;
}

.author-four.now-in-view {
  top: 77%;
  inset-inline-start: 53px;
}

.author-five.now-in-view {
  inset-inline-start: 50px;
  top: 41%;
}

.testi-author img {
  text-align: end;
  width: 150px;
  border-radius: 50%;
  height: 150px;
  object-fit: cover;
}

.ta-info {
  overflow: hidden;
  display: block;
}

.ta-info h6 {
  font-size: 20px;
  margin-bottom: 5px;
  color: #fff;
}

.ta-info span {
  color: #fff;
  font-size: 16px;
}

.testi-author {
  overflow: hidden;
}

.single-testimonial p {
  margin-bottom: 0;
}

.testimonial-active .slick-arrow {
  position: absolute;
  bottom: -80px;
  inset-inline-start: 0;
  border: none;
  background: none;
  padding: 0;
  font-size: 24px;
  color: #fff;
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
  width: 94%;
}

.testimonial-active i {
  font-size: 30px;
  color: #fff;
  margin-bottom: 30px;
}

.testimonial-active .slick-next {
  inset-inline-start: 40px;
}

.testimonial-active .slick-arrow:hover {
  color: var(--primary-color);
}

.testimonial-avatar.p-relative {
  min-height: 455px;
  display: block;
  overflow: hidden;
}

.testimonial-avatar .ta {
  position: absolute;
  inset-inline-start: 50%;
  top: 50%;
  overflow: hidden;
  transition: all 1200ms ease;
  -moz-transition: all 1200ms ease;
  -webkit-transition: all 1200ms ease;
  -ms-transition: all 1200ms ease;
  -o-transition: all 1200ms ease;
}

.avatar-one.now-in-view {
  top: 0%;
  inset-inline-start: 28%;
}

.avatar-two.now-in-view {
  top: 50%;
  inset-inline-start: 0;
  transform: translateY(-50%);
}

.avatar-three.now-in-view {
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
}

.avatar-four.now-in-view {
  top: 20%;
  inset-inline-start: 70%;
}

.avatar-five.now-in-view {
  top: 70%;
  inset-inline-start: 25%;
}

.avatar-six.now-in-view {
  top: 59%;
  inset-inline-start: 64%;
}

.single-testimonial-bg {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.1019607843);
  border-radius: 10px;
  width: 85%;
  float: right;
  padding: 30px 50px 100px 150px;
  margin: 20px;
  color: #525c7b;
}

.testimonial-active .slick-slide {
  position: relative;
  padding: 30px;
  margin: 0 15px 15px;
  background: #fff;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.08);
}

.testimonial-active .testi-img {
  float: left;
  margin-bottom: 20px;
  position: absolute;
  inset-inline-start: 0;
  top: 80px;
}

.testimonial-active .ta-info {
  overflow: hidden;
  float: left;
  padding-top: 0;
  margin-inline-start: 15px;
}

.testimonial-active .ta-info span {
  color: var(--primary-color);
  font-size: 16px;
}

.testimonial-active .ta-info h6 {
  font-size: 26px;
  margin-top: 5px;
  margin-bottom: 5px;
  color: var(--primary-color);
  font-weight: 700;
}

.testimonial-active .com-icon {
  position: absolute;
  inset-inline-end: 50px;
  top: 45px;
}

.testimonial-active .slick-dots {
  text-align: center;
  position: relative;
  width: 100%;
  margin-top: 15px;
}

.testimonial-active .slick-dots li {
  display: inline-block;
  margin: 0 0px;
  line-height: 0;
}

.testimonial-active .slick-dots li button {
  text-indent: -99999px;
  border: none;
  padding: 0;
  margin-inline-start: 10px;
  z-index: 1;
  cursor: pointer;
  transition: 0.3s;
  background-color: #ccc;
  height: 10px;
  width: 10px;
  border-radius: 2px;
}

.testimonial-active .slick-dots .slick-active button {
  width: 10px;
  background-color: var(--primary-color);
}

.testimonial-active .qt-img {
  position: absolute;
  inset-inline-end: 30px;
  top: 30px;
}

.testimonial-active .review-icon {
  margin: 30px 0;
}

.testimonial-active2 .qt-img {
  margin-bottom: 30px;
}

.testimonial-active2 .qt-img img {
  display: inline-block;
}

.testimonial-active2 .slider-nav {
  float: right;
  width: 100%;
}

.slider-nav .slick-list.draggable {
  width: 233px;
  float: right;
}

.testimonial-active2 .slick-arrow {
  position: absolute;
  top: inherit;
  inset-inline-start: inherit;
  border: none;
  background: none;
  padding: 0;
  font-size: 24px;
  color: #fff;
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
  inset-inline-end: -96px;
  bottom: 0;
}

.testimonial-active2 .slick-next {
  inset-inline-start: inherit;
  inset-inline-end: -165px;
}

.testimonial-active2 .slick-arrow:hover {
  color: var(--primary-color);
}

.testimonial-avatar.p-relative {
  min-height: 455px;
  display: block;
  overflow: hidden;
}

.testimonial-active2 .slick-next.slick-arrow i,
.testimonial-active2 .slick-arrow i {
  border-radius: 50%;
  background-color: rgb(80, 82, 171);
  box-shadow: 2.5px 4.33px 15px 0px rgba(80, 82, 171, 0.4);
  width: 60px;
  height: 60px;
  line-height: 60px;
}

.testimonial-active2 .ta-info span {
  color: var(--primary-color);
  font-size: 16px;
}

.testimonial-active2 .testi-author {
  border-bottom: none;
  padding-bottom: 0;
}

.testimonial-active2 .ta-info h6 {
  font-size: 24px;
  margin-bottom: 5px;
  color: var(--primary-color);
}

.com-icon {
  position: absolute;
  inset-inline-end: 0;
  top: 20px;
}

.testimonial-item img {
  width: 80px;
  cursor: pointer;
}

.testimonial-active2 .single-testimonial {
  text-align: center;
}

.testimonial-active2 .testi-author img {
  float: none;
  margin-bottom: 0;
  display: inline-block;
}

.testimonial-active2 .slick-dots li {
  display: inline-block;
  margin: 0 0px;
}

.testimonial-active2 .slick-dots {
  text-align: center;
  position: relative;
  width: 100%;
  margin-top: 25px;
}

.testimonial-active2 .slick-dots li button {
  text-indent: -99999px;
  border: none;
  padding: 0;
  margin-inline-start: 10px;
  z-index: 1;
  cursor: pointer;
  background: no-repeat;
  transition: 0.3s;
  background-color: #ccc;
  height: 10px;
  width: 10px;
  border-radius: 5px;
}

.testimonial-active2 .ta-info {
  overflow: hidden;
}

.testimonial-active2 .single-testimonial p {
  width: 60%;
  margin: auto;
  margin-bottom: 30px;
}

.testimonial-active2 .slick-dots .slick-active button {
  border-radius: 5px;
  background-color: var(--primary-color);
}

.testimonial-area .test-an-01 {
  position: absolute;
  bottom: 0;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.testimonial-area .test-an-02 {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
}

.test-line {
  margin-top: 30px;
  margin-bottom: 30px;
}

.test-line img {
  display: inline-block;
}

/* breadcrumb */
.breadcrumb-area {
  position: relative;
  background-repeat: no-repeat;
  background-color: #000;
  background-position: center center;
  /* Default: Use cover for most screen sizes */
  background-size: cover;
  min-height: 450px;
  /* For mobile devices - use cover with taller height, cropping sides is okay */
  /* For tablets (both portrait and landscape) - use cover with taller height */
  /* Specific handling for common laptop resolution (1366x768) and similar */
  /* For larger desktop screens (1600px+), use cover to avoid side bars */
  /* For ultra-wide screens (2560px+), we can use contain */
}
@media (max-width: 767px) {
  .breadcrumb-area {
    background-size: cover !important;
    background-position: 60% center !important;
    min-height: 350px;
  }
}
@media (min-width: 768px) and (max-width: 1199px) {
  .breadcrumb-area {
    background-size: cover !important;
    background-position: 60% center !important;
    min-height: 450px;
  }
}
@media (min-width: 1200px) and (max-width: 1599px) {
  .breadcrumb-area {
    background-size: cover;
    min-height: 350px;
  }
}
@media (min-width: 1600px) {
  .breadcrumb-area {
    background-size: cover;
    min-height: 400px;
  }
}
@media (min-width: 2560px) {
  .breadcrumb-area {
    background-size: contain;
    min-height: 600px;
  }
}

.breadcrumb {
  display: inline-block;
  -ms-flex-wrap: wrap;
  flex-wrap: unset;
  padding: 0;
  margin-bottom: 0;
  list-style: none;
  background-color: unset;
  border-radius: 0;
}

.breadcrumb li {
  display: inline-block;
}

.breadcrumb li a {
  font-size: 16px;
  color: #fff;
  font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-inline-end: 15px;
  padding-inline-start: 10px;
  color: #ddd;
  content: "|";
}

.breadcrumb-title h2 {
  font-size: 60px;
  margin-bottom: 25px;
  line-height: 1;
  color: #fff;
  letter-spacing: 1px;
  margin-top: 60px;
}

.breadcrumb-title p {
  margin-bottom: 0;
  color: #777;
  font-size: 16px;
}

.breadcrumb > .active {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500;
}

.cart-top {
  display: inline-block;
}

.cart-top li {
  display: inline-block;
  padding: 0px 17px;
}

.cart-top li a {
  color: #fff;
  font-size: 18px;
  float: none !important;
}

.cart-top li:last-child {
  border: none;
}

.call-box .icon {
  display: inline-block;
}

.call-box li {
  float: left;
  color: #fff;
}

.call-box .text {
  margin-inline-start: 10px;
}

.call-box span {
  display: block;
  color: rgba(255, 255, 255, 0.6705882353);
  padding: 3px;
}

.call-box strong {
  font-size: 22px;
}

.header-social a {
  color: #fff;
  margin-inline-start: 15px;
  font-size: 18px;
}

/* 16. pagination */
.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: block;
  padding-inline-start: 0;
  list-style: none;
  border-radius: 0;
}

.pagination .page-item {
  display: inline-block;
  margin: 0 5px;
}

.pagination .page-item a.page-link {
  border-radius: 50%;
  padding: 0;
  height: 70px;
  width: 70px;
  line-height: 70px;
  background: #f8f8f8;
  color: #9fa7c5;
  font-size: 14px;
  border: none;
  font-weight: 500;
}

.pagination .page-item:hover a.page-link {
  box-shadow: 0px 16px 32px 0px rgba(255, 74, 87, 0.2);
  background: #ff4a57;
  color: #fff;
}

.pagination .page-item.active a.page-link {
  box-shadow: 0px 16px 32px 0px rgba(255, 74, 87, 0.2);
  background: #ff4a57;
  color: #fff;
}

.pagination-wrap .pagination {
  display: block;
  border-radius: unset;
}

.pagination-wrap .pagination li {
  display: inline-block;
  margin-inline-end: 6px;
  margin-inline-start: 0;
  margin-bottom: 15px;
}

.pagination-wrap .pagination li.active a {
  background: #252525;
  color: #ffffff;
  box-shadow: 0px 8px 16px 0px rgba(26, 35, 126, 0.32);
}

.pagination-wrap .pagination li a {
  border: none;
  height: 50px;
  width: 50px;
  display: block;
  line-height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  color: #fff;
  font-size: 14px;
  text-align: center;
}

.pagination-wrap .pagination li a:hover {
  color: #ffffff;
  background: var(--primary-color);
}

.f-cta-area {
  background: #fff;
  margin-inline-start: 260px;
  margin-inline-end: 260px;
  position: relative;
  top: -70px;
  margin-bottom: 30px;
  box-shadow: 0 -3px 65px 0 rgba(0, 0, 0, 0.09);
}

.f-cta-icon {
  margin-inline-end: 30px;
}

.single-cta {
  line-height: 28px;
}

.single-cta a {
  color: #101010;
}

.f-cta-icon i {
  display: inline-block;
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 78px;
  background: var(--primary-color);
  border-radius: 50%;
  color: #fff;
  font-size: 28px;
}

.contact-info .single-cta {
  float: left;
}

.contact-info h5 {
  color: #101010;
}

.single-cta {
  border-bottom: 1px solid #ccc;
  float: left;
  width: 100%;
}

div.single-cta:last-child {
  border: 0;
}

.single-cta h5 {
  font-size: 20px;
  margin-bottom: 15px !important;
}

.single-cta p {
  margin-bottom: 0;
}

.single-cta p a {
  color: #777;
}

.s-cta-btn .btn {
  color: #fff;
  margin-top: 10px;
  border: none;
}

.widget.widget_media_image {
  margin: 25px 0 0;
}

.award-box .date {
  font-weight: 700;
  font-size: 24px;
  color: #101010;
  font-family: var(--primary-font), sans-serif;
}

.aw-line {
  position: relative;
}

.aw-line::before {
  content: "";
  position: absolute;
  height: 1px;
  inset-inline-start: 9%;
  border-bottom: 1px dashed;
  top: 32%;
  width: 81%;
  margin: auto;
}

/* 24. call */
.call-text {
  padding: 120px 0 0 60px;
}

.call-area h5 {
  color: var(--primary-color);
}

.call-area h2 {
  color: #fff;
  font-size: 58px;
  margin-bottom: 30px;
}

.call-area h2 span {
  color: var(--primary-color);
}

.call-area p {
  color: #fff;
  opacity: 0.9;
}

.number {
  font-size: 55px;
  font-weight: 600;
  color: var(--primary-color);
}

/*Checkout */
.coupon-accordion h3 {
  background-color: #fff1f0;
  border-top: 3px solid var(--primary-color);
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 25px;
  padding: 1em 2em 1em 3.5em;
  position: relative;
  width: auto;
}

.coupon-accordion h3::before {
  content: "\f07b";
  left: 15px;
  top: 13px;
  position: absolute;
  color: #6f7172;
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
}

.coupon-accordion span {
  color: #6f7172;
  cursor: pointer;
  transition: 0.3s;
}

.coupon-accordion span:hover,
p.lost-password a:hover {
  color: var(--primary-color);
}

.coupon-content {
  border: 1px solid #eaedff;
  display: none;
  margin-bottom: 20px;
  padding: 30px;
}

.coupon-info p.coupon-text {
  margin-bottom: 15px;
}

.coupon-info p {
  margin-bottom: 0;
}

.coupon-info p.form-row-first label,
.coupon-info p.form-row-last label {
  display: block;
  color: #6f7172;
}

.coupon-info p.form-row-first label span.required,
.coupon-info p.form-row-last label span.required {
  color: red;
  font-weight: 600;
}

.coupon-info p.form-row-first input,
.coupon-info p.form-row-last input {
  border: 1px solid #eaedff;
  height: 45px;
  margin: 0 0 14px;
  max-width: 100%;
  padding: 0 0 0 10px;
  width: 100%;
}

.coupon-info p.form-row input[type=submit]:hover,
p.checkout-coupon input[type=submit]:hover {
  background: var(--primary-color) none repeat scroll 0 0;
}

.coupon-info p.form-row input[type=checkbox] {
  position: relative;
  top: 2px;
}

.form-row > label {
  margin-top: 15px;
  margin-inline-start: 15px;
  color: #6f7172;
}

.buttons-cart input,
.coupon input[type=submit],
.buttons-cart a,
.coupon-info p.form-row input[type=submit] {
  background: #101010 none repeat scroll 0 0;
  border: medium none;
  color: #fff;
  display: inline-block;
  float: left;
  font-size: 12px;
  font-weight: 600;
  height: 40px;
  line-height: 40px;
  margin-inline-end: 15px;
  padding: 0 15px;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
}

p.lost-password {
  margin-top: 15px;
}

p.lost-password a {
  color: #6f6f6f;
}

p.checkout-coupon input[type=text] {
  height: 45px;
  padding: 0 15px;
  width: 100%;
  border: 1px solid #eaedff;
  margin-bottom: 15px;
}

.coupon-checkout-content {
  display: none;
}

.checkbox-form h3 {
  border-bottom: 1px solid #eaedff;
  font-size: 26px;
  margin: 0 0 20px;
  padding-bottom: 10px;
  width: 100%;
}

.country-select {
  margin-bottom: 30px;
  position: relative;
}

.country-select label,
.checkout-form-list label {
  color: #6f7172;
  display: block;
  margin: 0 0 5px;
}

.country-select label span.required,
.checkout-form-list label span.required {
  color: red;
}

.country-select select {
  -moz-appearance: none;
  border: 1px solid #eaedff;
  height: 45px;
  padding-inline-start: 10px;
  width: 100%;
  color: #6f7172;
}

.country-select::before {
  content: "\f107";
  inset-inline-end: 15px;
  top: 38px;
  position: absolute;
  color: #6f7172;
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
}

.checkout-form-list {
  margin-bottom: 30px;
}

.checkout-form-list label {
  color: #6f7172;
}

.checkout-form-list input[type=text],
.checkout-form-list input[type=password],
.checkout-form-list input[type=email] {
  background: #ffffff;
  border: 1px solid #eaedff;
  border-radius: 0;
  height: 45px;
  padding: 0 0 0 10px;
  width: 100%;
}

.checkout-form-list input[type=text]::-moz-placeholder,
.checkout-form-list input[type=password]::-moz-placeholder,
.checkout-form-list input[type=email]::-moz-placeholder {
  color: #6f7172;
  opacity: 1;
}

.checkout-form-list input[type=text]::placeholder,
.checkout-form-list input[type=password]::placeholder,
.checkout-form-list input[type=email]::placeholder {
  color: #6f7172;
  opacity: 1;
}

.checkout-form-list input[type=checkbox] {
  display: inline-block;
  margin-inline-end: 10px;
  position: relative;
  top: 1px;
}

.create-acc label {
  color: #6f7172;
  display: inline-block;
}

.create-account {
  display: none;
}

.ship-different-title h3 label {
  display: inline-block;
  margin-inline-end: 20px;
  color: #6f7172;
}

.order-notes textarea {
  border: 1px solid #eaedff;
  height: 90px;
  padding: 15px;
  width: 100%;
}

.order-notes textarea::-moz-placeholder {
  color: #6f7172;
  opacity: 1;
}

.order-notes textarea::placeholder {
  color: #6f7172;
  opacity: 1;
}

#ship-box-info {
  display: none;
}

.panel-group .panel {
  border-radius: 0;
}

.panel-default > .panel-heading {
  border-radius: 0;
}

.your-order {
  padding: 30px 40px 45px;
  border: 3px solid #eaedff;
}

@media (max-width: 767px) {
  .your-order {
    padding: 15px;
  }
}
.your-order h3 {
  border-bottom: 1px solid #eaedff;
  font-size: 30px;
  margin: 0 0 20px;
  padding-bottom: 10px;
  width: 100%;
}

.your-order-table table {
  background: none;
  border: 0;
  width: 100%;
}

.your-order-table table th,
.your-order-table table td {
  border-bottom: 1px solid #eaedff;
  border-inline-end: medium none;
  color: #6f7172;
  font-size: 14px;
  padding: 15px 0;
  text-align: start;
}

@media (max-width: 767px) {
  .your-order-table table th,
  .your-order-table table td {
    padding-inline-end: 10px;
  }
}
.your-order-table table th {
  border-top: medium none;
  color: #6f7172;
  font-weight: normal;
  text-align: start;
  vertical-align: middle;
  white-space: nowrap;
  width: 250px;
}

.panel-body > p {
  color: #222;
}

.your-order-table table .shipping ul li input {
  position: relative;
  top: 2px;
}

.your-order-table table .shipping ul li label {
  color: #6f7172;
}

.your-order-table table .shipping th {
  vertical-align: top;
}

.your-order-table table .order-total th {
  border-bottom: 0;
  font-size: 14px;
}

.your-order-table table .order-total td {
  border-bottom: medium none;
}

.your-order-table table tr.cart_item:hover {
  background: #f9f9f9;
}

.your-order-table table tr.order-total td span {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 500;
}

.payment-method {
  margin-top: 40px;
}

.panel-title > a {
  display: block;
}

.order-button-payment input {
  background: #232323 none repeat scroll 0 0;
  border: medium none;
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  height: 40px;
  margin: 0px 0 0;
  padding: 0;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
  width: 100%;
}

.order-button-payment input:hover {
  background: #fe4536 none repeat scroll 0 0;
}

.payment-method .btn-link {
  -moz-user-select: none;
  background: no-repeat;
  border: medium none;
  border-radius: 0;
  color: #444;
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 1;
  margin-bottom: 0;
  padding: 3px 10px;
  text-align: center;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
  vertical-align: middle;
  white-space: nowrap;
  text-decoration: none;
}

.payment-method .card {
  background-color: #ffffff;
  border: 1px solid #eaedff;
  border-radius: 0;
  margin-bottom: 10px;
}

.payment-method .accordion .card:first-of-type {
  border: 1px solid #eaedff;
}

.card-header:first-child {
  border-radius: 0;
}

.payment-method .card-header {
  background-color: #ffffff;
  border-bottom: 1px solid #eaedff;
}

.order-button-payment button {
  width: 100%;
}

.country-select .nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-radius: 0;
  border: solid 1px #eaedff;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  float: left;
  font-family: inherit;
  font-size: 14px;
  font-weight: normal;
  height: 45px;
  line-height: 45px;
  outline: none;
  padding-inline-start: 18px;
  padding-inline-end: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 100%;
  margin-bottom: 19px;
}

/* 18. pricing */
.pricing-area .nav {
  box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.0784313725);
  margin-top: 50px;
  border-radius: 30px;
  padding: 15px;
}

.pricing-area .nav-pills .nav-link {
  padding: 15px 30px;
}

.pricing-area .nav-pills .nav-link.active {
  background: #4cc3c1;
  padding: 15px 40px;
  border-radius: 30px;
}

.pricing-area .nav-pills .nav-link span {
  background: #ffebe4;
  padding: 5px 9px;
  font-size: 12px;
  border-radius: 15px;
  color: var(--primary-color);
  font-weight: 600;
}

.pricing-box {
  float: left;
  width: 100%;
  position: relative;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.08);
  padding: 50px;
}

.pricing-head {
  float: left;
  width: 100%;
  position: relative;
}

.pricing-head .month {
  position: absolute;
  inset-inline-end: -26px;
  top: 26px;
  border-radius: 4px;
  background-color: rgb(225, 225, 225);
  padding: 3px 10px;
  transform: rotate(90deg);
}

.pricing-head h3 {
  font-size: 26px;
  display: inline-block;
  font-weight: 700;
}

.price-count sub {
  font-size: 14px;
  bottom: 5px;
}

.pricing-head .glyph-icon::before {
  font-size: 32px;
  margin-inline-start: 0;
  width: 85px;
  background: #3763eb;
  height: 85px;
  display: inline-block;
  line-height: 65px;
  border-radius: 50%;
  color: #fff;
  margin-bottom: 30px;
  border: 10px solid #eee;
}

.pricing-head h4 {
  font-size: 30px;
  font-weight: 600;
  color: #101010;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.price-count h2 {
  font-size: 50px;
  font-weight: 700 !important;
  margin-bottom: 0 !important;
  color: var(--primary-color);
}

.price-count h2 strong {
  font-size: 15px;
  margin-inline-start: -12px;
  color: #777;
  font-weight: 700;
}

.price-count small {
  font-size: 18px;
  font-weight: 600;
  position: relative;
  top: -20px;
  margin-inline-end: 3px;
}

.price-count span {
  color: #101010;
  font-size: 15px;
  margin-inline-start: -10px;
}

.pricing-body {
  padding: 0px;
  width: 100%;
}

.pricing-body li {
  margin-top: 15px;
  list-style: none !important;
}

.pricing-body li:first-child {
  margin-top: 0;
}

.pricing-body li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  color: var(--primary-color);
  width: 25px;
  height: 25px;
  position: relative;
  float: left;
  text-align: center;
  border: 2px solid;
  font-size: 12px;
  line-height: 22px;
  margin-top: 2px;
  border-radius: 50%;
  margin-inline-end: 15px;
}

.pricing-body p {
  margin-bottom: 15px;
  font-size: 14px;
}

.pricing-box.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.pricing-box.active .pricing-head .glyph-icon::before {
  background: #fff;
  color: #3763eb;
}

.pricing-box.active .pricing-head h4 {
  color: #fff;
}

.pricing-body {
  float: left;
}

.pricing-head h5 {
  font-size: 30px;
  margin-bottom: 15px !important;
}

.pricing-box2 .icon {
  margin-bottom: 15px;
}

.pricing-box2 .icon img {
  height: 40px;
}

.pricing-box2 hr {
  background: #d7d7d7;
  margin: auto;
  padding: 0;
  display: inline-block;
  width: 100%;
  height: 2px;
  border: none;
}

.pricing-box2.active {
  background: var(--primary-color);
}

.pricing-box2.active .price-count h2,
.pricing-box2.active .pricing-body li,
.pricing-box2.active .pricing-body li::before,
.pricing-box2.active .pricing-head h3,
.pricing-box2.active .pricing-head p {
  color: #fff;
}

.pricing-box2.active .pricing-head .month {
  background-color: rgba(225, 225, 225, 0.18);
  color: #fff;
}

.pricing-box.active .pricing-btn .btn {
  background: #fff;
  color: var(--primary-color);
}

.pricing-box2 .pricing-head p {
  margin-bottom: 10px;
  margin-top: 0;
}

.pricing-box2 .price-count {
  display: inline-block;
  margin-top: 10px;
}

.pricing-box2 .price-count h2 {
  font-size: 50px;
}

.pricing-btn .btn {
  width: 100%;
}

.pricing-btn .btn::before {
  display: none;
}

/* 20. footer */
.recent-blog-footer li {
  display: flex;
  margin-bottom: 25px !important;
}

.recent-blog-footer li .thum {
  float: left;
  width: 155px;
  margin-inline-end: 10px;
}

.recent-blog-footer li .thum img {
  border-radius: 10px;
}

.recent-blog-footer a {
  width: 100%;
  display: inline-block;
  color: rgba(255, 255, 255, 0.8) !important;
}

.recent-blog-footer a:hover {
  color: #fff !important;
}

.recent-blog-footer span {
  color: var(--primary-color);
}

.footer-top-heiding {
  margin-bottom: 50px;
}

.footer-top-heiding .container {
  padding-bottom: 50px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1803921569);
}

.f-contact {
  display: flex;
}

.f-contact i {
  width: 40px;
  height: 40px;
  background: #fff;
  line-height: 40px;
  color: #412720 !important;
  text-align: center;
  border-radius: 50%;
  float: left;
}

.f-contact a {
  color: rgba(255, 255, 255, 0.8) !important;
}

.f-contact a:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.footer-top-heiding .col-lg-4 {
  border-inline-end: 2px solid #20282e;
}

.footer-top-heiding .col-lg-4:last-child {
  border-inline-end: none;
}

.f-contact span {
  color: rgba(255, 255, 255, 0.8);
}

.f-contact h3 {
  color: #fff;
  font-size: 24px;
}

.footer-top-heiding h2 {
  color: #fff;
}

.footer-bg {
  color: #777;
  background-position: -180px center !important;
  background-size: cover;
}

.f-cta-area.gray-bg {
  background: #f4f4fe;
  border-top: 3px solid var(--primary-color);
}

.footer-text p {
  color: #ddd;
  margin-bottom: 0;
}

.footer-social span {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  font-family: "Playfair Display", serif;
  display: inline-block;
  margin-inline-end: 20px;
}

.footer-social a {
  font-size: 16px;
  display: inline-block;
  margin-inline-end: 10px;
  width: 40px;
  height: 40px;
  border-radius: 5px;
  background-color: #101010;
  text-align: center;
  line-height: 40px;
  color: #fff !important;
}

.footer-social a:hover {
  background: #101010;
  color: var(--secondary-color) !important;
}

.f-widget-title h2,
.footer-widget .widgettitle {
  font-size: 24px;
  margin-bottom: 5px;
  position: relative;
  padding-bottom: 30px;
  color: #fff;
}

.f-widget-title h2::before,
.footer-widget .widgettitle::before {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
  height: 1px;
  width: 70px;
}

.f-widget-title img {
  width: 146px;
}

.footer-link ul li i {
  color: var(--primary-color);
  margin-inline-end: 5px;
}

.footer-link ul li a {
  color: rgba(255, 255, 255, 0.8);
  display: inline-block;
}

.footer-link ul li a::before {
  content: "+";
  margin-inline-end: 8px;
  color: var(--primary-color);
  font-size: 18px;
  display: none;
}

.footer-link ul li a:hover {
  color: #fff;
}

.footer-link ul li {
  margin-bottom: 10px;
}

.footer-link ul li:last-child {
  margin-bottom: 0;
}

.f-insta ul li {
  display: inline-block;
  margin-bottom: 15px;
  margin-inline-end: 9px;
  width: 31.2% !important;
}

.f-insta ul li img {
  width: 100%;
}

.f-insta ul li:nth-child(3),
.f-insta ul li:nth-child(6) {
  margin-inline-end: 0 !important;
}

.f-insta ul li a {
  display: block;
  position: relative;
}

.f-insta ul li a::before {
  content: "\f067";
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  top: 40%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #fff;
  transition: 0.3s;
  opacity: 0;
  z-index: 9;
}

.f-insta ul li a::after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: var(--primary-color);
  opacity: 0;
  transition: 0.3s;
}

.f-insta ul li:hover a::before {
  top: 50%;
  opacity: 1;
}

.f-insta ul li:hover a::after {
  opacity: 0.6;
}

.copyright-wrap {
  background: #282828;
  color: #fff;
  font-size: 15px;
}

.copyright-wrap .container {
  padding: 20px 0;
}

.copyright-wrap li {
  display: inline;
  padding-inline-start: 15px;
  margin-inline-start: 40px;
  position: relative;
}

.copyright-wrap li::before {
  background-color: #101010;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  inset-inline-start: -4px;
  top: 5px;
  border-radius: 50px;
}

.copyright-wrap li a {
  color: #fff;
}

.f-contact .icon {
  float: left;
  margin-top: 7px;
  margin-inline-end: 15px;
  width: 40px;
}

.f-contact li {
  float: left;
  margin-bottom: 15px !important;
  width: 100%;
}

.f-contact li:last-child {
  margin-bottom: 0px !important;
}

#contact-form4 {
  margin-top: 20px;
}

#contact-form4 input {
  padding: 23px 30px;
  border: none;
  width: 75%;
}

#contact-form4 button {
  padding: 15px 30px;
  border-radius: 0;
}

.main-menu ul li.has-sub .current a {
  color: #222;
}

.main-menu ul li.current a {
  color: var(--primary-color);
}

.main-menu .has-sub > ul {
  visibility: hidden;
  position: absolute;
  background-color: white;
  min-width: 250px;
  z-index: 1;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
  border-top: 4px solid var(--primary-color);
  box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  opacity: 0;
}

.main-menu .has-sub li a {
  padding: 15px;
}

.main-menu .has-sub li {
  margin-inline-start: 0 !important;
  float: left;
  border-bottom: 1px solid #ddd;
  width: 100%;
  text-align: start;
}

.main-menu a[target=_self]:hover {
  color: #aa7769;
}

.main-menu .has-sub:hover > ul {
  visibility: visible;
  margin-top: 0px;
  opacity: 1;
}

.main-menu .has-sub > ul > .has-sub > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: -255px;
  margin-top: 0;
  top: -3px;
}

.main-menu .has-sub > ul > .has-sub:hover > ul {
  display: block;
}

.main-menu .has-sub > ul > .has-sub > ul > .has-sub > ul {
  display: none;
  position: absolute;
  background-color: white;
  width: 250px;
  z-index: 1;
  padding: 0;
  margin-inline-start: 255px;
  margin-top: 0;
  top: -3px;
}

.main-menu .has-sub > ul > .has-sub > ul > .has-sub:hover > ul {
  display: block;
}

.main-menu .has-sub ul li a {
  color: #4f4f4f;
}

.main-menu .has-sub ul li a:hover {
  color: var(--primary-color);
}

#scrollUp {
  background: var(--primary-color);
  height: 45px;
  width: 45px;
  inset-inline-end: 50px;
  bottom: 77px;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  font-size: 22px;
  line-height: 45px;
  transition: 0.3s;
}

#scrollUp:hover {
  background: var(--primary-color);
}

/* faq */
.faq-area .section-title h2 {
  color: #fff;
}

.faq-img {
  margin-inline-end: -350px;
}

.faq-btn {
  font-size: 17px;
  font-weight: 700;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 20px 30px;
  width: 100%;
  text-align: start;
  padding-inline-end: 75px;
  background: #f8f8f8;
  border-radius: 0;
}

.card .collapse.show {
  display: block;
}

.faq-btn.collapsed {
  background: no-repeat;
  color: #190a32;
}

.faq-wrap .card-header:first-child {
  border-radius: 0;
}

.faq-wrap .card-header {
  padding: 0;
  margin-bottom: 0;
  background-color: unset;
  border-bottom: none;
}

.faq-wrap .card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: unset;
  padding: 25px 30px;
}

.faq-wrap .card-header h2 {
  font-size: unset;
}

.faq-wrap .card {
  border: none;
  border-radius: 4px !important;
  margin-bottom: 15px;
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.09);
  overflow: hidden;
}

.faq-wrap .card-header h2 button::after {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  content: "\f107";
  top: 0;
  inset-inline-end: 0;
  font-size: 26px;
  font-family: "Font Awesome 5 Pro";
  font-weight: 600;
  background: var(--primary-color);
  width: 61px;
  height: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1);
  text-align: center;
  line-height: 60px;
  color: #fff;
  transform: rotateY(180deg);
}

.faq-wrap .card-header h2 button.collapsed::after {
  content: "\f104";
  color: #fff;
  box-shadow: none;
  font-size: 26px;
}

/* Load More Option */
.load-more-option {
  position: relative;
  display: block;
  width: 100%;
  text-align: center;
  margin-top: 70px;
}

.load-more-option li.prev a,
.load-more-option li.next a {
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 20px;
  width: 80px;
  padding: 10px 10px;
  text-align: center;
  color: #ffffff;
  background-color: var(--primary-color);
  border-radius: 10px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.load-more-option li.prev a:hover,
.load-more-option li.next a:hover {
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.05);
}

.load-more-option .load-more {
  position: relative;
  display: inline-block;
  font-size: 30px;
}

.load-more-option .load-more a {
  position: relative;
  display: inline-block;
  font-size: 30px;
  line-height: 42px;
  color: var(--primary-color);
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.load-more-option .load-more a:hover {
  color: #000000;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.video2 {
  height: auto;
  min-height: 100%;
  min-width: 100%;
  position: absolute;
  top: -250px;
  width: auto;
  z-index: 0;
}

/* 27. map-area */
.map-img li {
  position: absolute;
  display: inline-block;
  z-index: 999;
  top: 93px;
  inset-inline-start: 0;
}

.map-img li:nth-child(2) {
  position: absolute;
  top: 50%;
  inset-inline-start: 45%;
}

.map-img li:nth-child(2) .map-text-hover {
  inset-inline-start: -520px;
}

.map-img li:nth-child(3) {
  position: absolute;
  top: 100px;
  inset-inline-start: 85%;
}

.map-img li:nth-child(3) .map-text-hover {
  inset-inline-start: -520px;
}

.map-img li:nth-child(4) {
  position: absolute;
  top: 40%;
  inset-inline-start: 15%;
}

.map-img li:nth-child(4) .map-text-hover {
  inset-inline-start: 50px;
}

.map-img li:nth-child(5) {
  position: absolute;
  top: 43%;
  inset-inline-start: 70%;
}

.map-img li:nth-child(5) .map-text-hover {
  inset-inline-start: -520px;
}

.map-text-hover {
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 5px 83px 0px rgba(40, 40, 40, 0.15);
  position: absolute;
  width: 510px;
  height: 133px;
  z-index: 138;
  top: 0;
  inset-inline-start: 50px;
  padding: 30px;
  display: none;
}

.map-img li:hover .map-text-hover {
  display: block;
}

.map-content {
  border-radius: 3px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 30px 60px 0px rgba(40, 40, 40, 0.04);
  margin-top: -150px;
  position: relative;
}

.map-text-user {
  display: flex;
}

.map-text-user .text {
  padding: 30px 30px 0px 30px;
}

.map-text-user .img img {
  max-width: initial;
  height: 152px;
}

.map-line {
  height: 100%;
  width: 5px;
  background: #27ac60;
  position: absolute;
  inset-inline-start: 50%;
}

.map-cirl {
  border-radius: 50%;
  background-color: rgb(255, 255, 255);
  position: absolute;
  width: 60px;
  height: 60px;
  z-index: 146;
  border: 5px solid #27ac60;
  text-align: center;
  line-height: 50px;
  color: #27ac60;
  inset-inline-start: 47.4%;
  top: 43px;
}

.contact-area .container {
  position: relative;
  z-index: 1;
}

.contact-bg02 .slider-btn {
  width: 100%;
}

.contact-bg02 .slider-btn .btn {
  width: 100%;
}

.contact-bg02 .btn.ss-btn.active {
  background: var(--primary-color);
  border: 2px solid var(--primary-color);
  color: #fff;
}

.contact-field select {
  width: 100%;
  border: none;
  background-color: rgb(255, 255, 255);
  transition: 0.3s;
  border-radius: 0;
  border: 1px solid rgb(183, 183, 183);
  height: calc(2.5em + 0.75rem + 2px);
}

.contact-field .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #fff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgb(225, 225, 225);
}

.contact-field .form-control {
  color: #9e9e9e;
}

.contact-bg-an-01 {
  position: absolute;
  inset-inline-start: 9.8%;
  top: 25%;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.contact-bg-an-02 {
  position: absolute;
  inset-inline-end: 3%;
  bottom: 7%;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

@keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-20px);
    -moz-transform: rotateX(0deg) translateY(-20px);
    -ms-transform: rotateX(0deg) translateY(-20px);
    -o-transform: rotateX(0deg) translateY(-20px);
    transform: rotateX(0deg) translateY(-20px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
@-webkit-keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-20px);
    -moz-transform: rotateX(0deg) translateY(-20px);
    -ms-transform: rotateX(0deg) translateY(-20px);
    -o-transform: rotateX(0deg) translateY(-20px);
    transform: rotateX(0deg) translateY(-20px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
/* meal */
.populer-meal li {
  display: inline-block;
  width: 33.14%;
  margin-bottom: 30px;
}

.meal-container {
  display: flex;
}

.meal-price strong {
  width: 100%;
  float: right;
  text-align: center;
  color: var(--primary-color);
  font-size: 25px;
  font-weight: 600;
  font-family: var(--primary-font), sans-serif;
}

.meal-container div {
  padding: 10px;
}

.meal-container a:hover {
  color: #ffce1c;
}

.meal-container .line {
  width: 12%;
  padding: 0 !important;
}

.meal-container .line hr {
  color: #fff;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 2px dashed;
}

.meal-price span {
  width: 100%;
  text-align: center;
  display: inline-block;
  margin-top: 10px;
  text-decoration: line-through;
  font-size: 16px;
  font-weight: 600;
}

.meal-content,
.meal-container .line,
.meal-price {
  margin-top: 15px;
}

.meal-content h5 {
  font-size: 24px;
}

/* 23. mega menu */
.offcanvas-menu .has-mega-menu ul {
  display: none;
}

.offcanvas-menu li.has-sub.has-mega-menu:hover > ul {
  margin-inline-start: -1160px;
  background: #00081b;
  top: 121px;
}

.offcanvas-menu .mega-menu-column.has-sub ul li a {
  color: #fff;
  font-size: 14px;
}

.offcanvas-menu .mega-menu-column.has-sub ul li a:hover {
  color: var(--primary-color);
}

.has-mega-menu {
  position: static;
}

li.has-sub.has-mega-menu ul {
  position: absolute;
  top: NNpx; /*insert the needed value*/
  z-index: 100;
  left: 0px;
  inset-inline-end: 0px;
  width: 1180px;
}

.main-menu .mega-menu-column.has-sub a {
  color: #333;
}

.main-menu .mega-menu-column.has-sub a:hover {
  color: var(--primary-color);
}

li.has-sub.has-mega-menu:hover > ul {
  /*flexbox fallback for browsers that do not support CSS GRID lyout*/
  display: flex;
  flex-wrap: wrap;
  /*CSS GRID lyout*/
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25%, 1fr));
  margin-inline-start: -820px;
}

li.has-sub.has-mega-menu:hover > ul > li > ul {
  /*flexbox fallback for browsers that do not support CSS GRID lyout*/
  display: flex;
  flex-wrap: wrap;
  width: auto;
  background: none;
  /*CSS GRID lyout*/
  display: grid;
  position: relative;
  border: none;
  margin-inline-start: 0;
}

@media screen and (min-width: 992px) {
  li.has-mega-menu:hover > ul {
    grid-template-columns: repeat(auto-fit, minmax(33.3333%, 1fr));
  }
}
@media screen and (min-width: 1200px) {
  li.has-mega-menu:hover > ul {
    grid-template-columns: repeat(auto-fit, minmax(25%, 1fr));
  }
}
@media screen and (min-width: 600px) {
  li.mega-menu-column {
    width: 100%;
    max-width: 100%;
    min-height: 1px;
    padding: 10px 25px;
    flex: 1 0 50%;
  }
}
@media screen and (min-width: 992px) {
  li.mega-menu-column {
    flex: 1 0 33.333%;
  }
}
.main-menu .has-sub .mega-menu-column li {
  flex: 1 0 25%;
  border: none;
}

.menu .children {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 250px;
  z-index: 1;
  transition: all 0.3s ease-in-out;
  margin-top: 15px;
  border-top: 4px solid var(--primary-color);
  box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
  -webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
}

.menu .sub-menu li,
.menu .children li {
  margin-inline-start: 0 !important;
  float: left;
  border-bottom: 1px solid #ddd;
  width: 100%;
  text-align: start;
}

.main-menu .sub-menu li a,
.menu .children li a {
  padding: 15px;
  color: #000;
}

.menu .menu-item-has-children:hover > ul,
.menu .page_item_has_children:hover > ul {
  display: block;
  margin-top: 0px;
}

.main-menu ul li.page_item {
  margin-inline-start: 15px;
}

.main-menu ul li.page_item::after {
  display: none;
}

.main-menu ul li.page_item.current_page_item {
  margin-inline-start: 50px;
}

.main-menu ul li.page_item.current_page_item::after {
  display: block;
}

/* 8. newslater-area */
.newslater .input-bg {
  background: #3a3d3e;
  border-radius: 30px !important;
  float: left;
  padding-inline-end: 5px;
  position: relative;
  z-index: 3;
  width: 40%;
}

.newslater .input-bg2 {
  background: #3a3d3e;
  border-radius: 30px !important;
  float: left;
  padding-inline-end: 5px;
  position: relative;
  z-index: 2;
  margin-inline-start: -35px;
  width: 40%;
}

.newslater input {
  background: #fff !important;
  border: 1px solid #fff !important;
  box-shadow: none !important;
  padding: 9px 20px !important;
  color: #030c26 !important;
  height: calc(1.5em + 0.75rem + 20px);
  width: 100% !important;
  position: relative;
  z-index: 1;
  margin-inline-end: 7px;
  border-radius: 10px;
}

.newslater .form-group {
  margin-bottom: 0;
}

.newslater button:hover {
  background: #ff3494;
  color: #fff;
}

.newslater *::-moz-placeholder {
  color: #939393;
  font-size: 14px;
  opacity: 0.8;
  opacity: 1;
}

.newslater *::placeholder {
  color: #939393;
  opacity: 0.8;
  font-size: 14px;
  opacity: 1;
}

.newslater-area #contact-form4 {
  width: 70%;
  display: inline-block;
  position: relative;
  margin: auto;
}

.newslater-area #contact-form4 button {
  background: var(--primary-color);
  color: #fff;
  height: 56px;
  z-index: 1;
  text-align: center;
  font-size: 15px;
  position: absolute;
  inset-inline-end: 0;
  top: 0;
  border: none;
  padding: 10px 20px;
  text-transform: uppercase;
  border-radius: 0;
}

.slick-slide {
  outline: none;
}

.features-area h5 {
  margin-bottom: 15px;
}

.features-area ul {
  margin-top: 30px;
  color: #fff;
  display: inherit;
}

.features-area li {
  display: flex;
  margin-bottom: 15px;
}

.features-area li .icon {
  margin-inline-end: 20px;
  color: #84daa9;
}

.features-area .nav-pills .nav-link {
  width: 100%;
  font-size: 24px;
  font-family: var(--primary-font), sans-serif;
  font-weight: 600;
}

.features-area .nav-pills .nav-link img {
  margin-inline-end: 15px;
}

.features-area .nav-pills .nav-link.active,
.features-area .nav-pills .show > .nav-link {
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
  color: #101010;
}

.feature-text h3 {
  font-size: 48px;
}

.fe-img {
  margin: 0 -40px;
}

.portfolio .col2 .grid-item {
  width: 49%;
  padding: 15px;
}

.portfolio .col3 .grid-item {
  width: 33.33%;
  margin: 0;
  padding: 15px;
}

.portfolio .col3 .grid-item .box {
  position: relative;
  overflow: hidden;
}

.portfolio .col3 .grid-item .box a::after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: #b68c5a;
  opacity: 0;
  transition: 0.3s;
}

.portfolio .col3 .grid-item .box a::before {
  content: "\f067";
  font-family: "Font Awesome 5 Pro" !important;
  position: absolute;
  top: 40%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #fff;
  transition: 0.3s;
  opacity: 0;
  z-index: 9;
}

.portfolio .col3 .grid-item .box a:hover::before {
  top: 50%;
  opacity: 1;
}

.portfolio .col3 .grid-item .box a:hover::after {
  opacity: 0.6;
}

.portfolio .col3 .grid-item .box20 img {
  width: auto;
  height: 380px;
  border-radius: 0px;
  max-width: inherit;
  margin-inline-start: -150px;
}

.portfolio .col4 .grid-item {
  width: 25%;
  margin: 0;
  padding: 0;
}

.gallery-image {
  overflow: hidden;
  position: relative;
  margin: 0;
}

.gallery-image figcaption {
  width: 100%;
  padding: 0 35px 30px 60px;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 500;
  position: absolute;
  bottom: -150px;
  z-index: 2;
  text-align: start;
}

.gallery-image::before {
  content: "\f067";
  font-family: "Font Awesome 5 Pro" !important;
  position: absolute;
  top: 40%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #fff;
  transition: 0.3s;
  opacity: 0;
  z-index: 1;
  background: var(--primary-color);
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
}

.gallery-image:hover::before {
  top: 50%;
  opacity: 1;
}

.gallery-image::after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: var(--primary-color);
  opacity: 0;
  transition: 0.3s;
}

.gallery-image:hover::after {
  opacity: 0.85;
}

.masonry-gallery-huge {
  margin: auto;
  padding-inline-end: 1.85%;
  padding-inline-start: 1.85%;
}

.profile .masonry-gallery-huge {
  margin: 0 -22px;
  padding-inline-end: 0;
  padding-inline-start: 0;
}

.profile .grid-item {
  margin: 0;
}

.grid-item {
  margin: 15px;
}

.gallery-image:hover figcaption {
  opacity: 1;
  bottom: 30px;
}

.gallery-image figcaption h4 {
  font-weight: 700;
  font-size: 36px;
  color: #fff;
}

.gallery-image figcaption .span {
  color: #b68c5a;
  font-size: 14px;
  position: relative;
  padding-inline-start: 40px;
  display: inline-block;
  margin-bottom: 0;
}

.gallery-image figcaption .span::before {
  content: "";
  width: 30px;
  position: absolute;
  height: 2px;
  background: #b68c5a;
  top: 50%;
  inset-inline-start: 0;
}

.gallery-image figcaption .span p {
  margin-bottom: 0;
}

.gallery-image figcaption .icon {
  position: absolute;
  inset-inline-end: 0;
  background: #3763eb;
  width: 45px;
  height: 45px;
  line-height: 45px;
  top: 0;
  color: #fff;
  text-align: center;
  font-size: 40px;
}

.button-group {
  padding-bottom: 15px;
}

.button-group button {
  border: none;
  background: none;
  transition: 0.3s;
  cursor: pointer;
  outline: none;
  color: #777;
  position: relative;
  padding-top: 0;
  margin-inline-start: 15px;
}

.button-group button:hover {
  color: #030c26;
}

.button-group button.active {
  color: #030c26;
  border-bottom: 1px solid var(--primary-color);
  font-weight: 500;
}

.button-group button::before {
  content: "/";
  position: relative;
  inset-inline-start: -12px;
}

.button-group button:first-child::before {
  display: none;
}

.gallery-image img {
  width: 100%;
  margin-bottom: 0;
}

/* 24. process-area */
.process-area h2 {
  color: #fff;
}

.process-area .section-title h5 {
  color: var(--primary-color);
}

.process-area .col-lg-3.col-md-12:nth-child(2),
.process-area .col-lg-3.col-md-12:nth-child(4) {
  margin-top: 50px;
}

.process-area .col-lg-3.col-md-12:nth-child(2) .process-icon,
.process-area .col-lg-3.col-md-12:nth-child(2) .no {
  background: var(--primary-color) 0% 0% no-repeat padding-box;
}

.process-area .col-lg-3.col-md-12:nth-child(3) .process-icon,
.process-area .col-lg-3.col-md-12:nth-child(3) .no {
  background: #27ae61 0% 0% no-repeat padding-box;
}

.process-area .col-lg-3.col-md-12:nth-child(4) .process-icon,
.process-area .col-lg-3.col-md-12:nth-child(4) .no {
  background: #8145da 0% 0% no-repeat padding-box;
}

.process-area h5 {
  color: #fff;
}

.process-icon {
  width: 180px;
  height: 180px;
  line-height: 180px;
  background: #4cc3c1 0% 0% no-repeat padding-box;
  text-align: center;
  border-radius: 50%;
  margin-bottom: 30px;
  position: relative;
  display: inline-block;
}

.process-area .no {
  position: absolute;
  top: 0;
  line-height: 40px;
  inset-inline-end: 0;
  width: 50px;
  height: 50px;
  text-align: center;
  background: #4cc3c1 0% 0% no-repeat padding-box;
  border: 4px solid #101010;
  border-radius: 50%;
  color: #ffffff;
  font-weight: 600;
}

/* 8. search-popup */
.search-popup {
  background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
  border: medium none;
  box-shadow: 0 0 0;
  margin-top: 200px;
  text-transform: uppercase;
}

.search-bg {
  background: #f5f5f5;
  opacity: 1;
}

.close2 {
  color: var(--primary-color);
  cursor: pointer;
  font-size: 14px;
}

.search-bg a i {
  color: var(--primary-color);
  font-size: 18px;
}

.search-outer {
  border-bottom: 1px solid #101010;
  float: left;
  margin-top: 100px;
  padding-bottom: 16px;
  width: 100%;
}

.search-outer input {
  background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
  border: medium none;
  color: #000;
  font-size: 14px;
  width: 100%;
}

.case-study-content {
  padding: 30px;
  background: #fff5f4 0% 0% no-repeat padding-box;
  box-shadow: 0px 16px 32px #fff5f4;
}

.case-study-content span {
  margin-bottom: 10px;
  display: inline-block;
}

.case-study-content02 {
  padding: 30px 30px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 16px 32px rgba(16, 16, 16, 0.0392156863);
}

.case-study-content02 span {
  margin-bottom: 10px;
  display: inline-block;
  color: var(--primary-color);
}

.case-study-content02 .ss-btn {
  display: none;
  transition: all 0.5s ease 0s;
}

.case-study-box:hover .case-study-content02 .ss-btn {
  display: inline-block;
}

.case-study-box .case-study-content02 {
  transition: all 0.5s ease 0s;
}

.case-study-box:hover .case-study-content02 {
  transform: translate(0, -62px);
}

/* 24. skills-area */
.feature-img {
  margin-inline-start: -330px;
}

.feature-title h5 {
  text-transform: capitalize;
  margin-bottom: 10px;
}

.feature-title h2 {
  font-size: 70px;
}

.team-area-content .skill-name {
  color: #101010;
}

.skills {
  width: 100%;
  padding: 0 20px 0 0;
}

.skills-img {
  margin-inline-end: -330px;
}

.skills-content p {
  color: #fff;
}

.skill-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0px 0 15px;
  color: #fff;
  font-family: var(--primary-font), sans-serif;
}

.skill-bar {
  height: 15px;
  background: #e0e0e0;
  border-radius: 0;
}

.skill-per {
  height: 15px;
  background-color: var(--secondary-color);
  border-radius: 0;
  width: 0;
  position: relative;
  transition: 1s linear;
}

.skill-per::before {
  content: attr(id);
  position: absolute;
  padding: 4px 6px;
  background-color: var(--secondary-color);
  color: #fff;
  font-size: 12px;
  border-radius: 4px;
  top: -43px;
  inset-inline-end: 0;
  transform: translateX(50%);
}

.skill-per::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: var(--secondary-color);
  top: -16px;
  inset-inline-end: 0;
  transform: translateX(50%) rotate(45deg);
  border-radius: 2px;
}

.skills-title h5 {
  color: #fff;
  font-size: 22px;
  text-transform: capitalize;
  margin-bottom: 10px;
}

.skills-title h2 {
  color: #fff;
  font-size: 70px;
}

.progress-outer li {
  padding-inline-start: 100px;
}

.progress-outer li:first-child {
  text-align: right;
  padding-inline-end: 50px;
  margin-bottom: -50px;
}

.progress-outer li:last-child {
  text-align: right;
  padding-inline-end: 50px;
  margin-top: -50px;
}

.progress-box {
  display: inline-block;
}

.progress {
  width: 235px;
  height: 235px;
  line-height: 235px;
  background: none;
  box-shadow: none;
  position: relative;
}

.progress:after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #e1e1e1;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
}

.progress > span {
  width: 50%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  z-index: 1;
}

.progress .progress-left {
  left: 0;
}

.progress .progress-bar {
  width: 100%;
  height: 100%;
  background: none;
  border-width: 12px;
  border-style: solid;
  position: absolute;
  top: 0;
}

.progress .progress-left .progress-bar {
  left: 101%;
  border-top-right-radius: 130px;
  border-bottom-right-radius: 130px;
  border-inline-start: 0;
  -webkit-transform-origin: center left;
  transform-origin: center left;
}

.progress .progress-right {
  inset-inline-end: 0;
}

.progress .progress-right .progress-bar {
  left: -101%;
  border-top-left-radius: 130px;
  border-bottom-left-radius: 130px;
  border-inline-end: 0;
  -webkit-transform-origin: center right;
  transform-origin: center right;
  animation: loading-1 1.8s linear forwards;
}

.progress .progress-value {
  width: 90%;
  height: 90%;
  border-radius: 50%;
  background: none;
  font-size: 48px;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 135px;
  text-align: center;
  position: absolute;
  top: 15%;
  left: 5%;
}

.progress .progress-value sub {
  font-size: 20px;
  color: var(--primary-color);
  margin-inline-start: 5px;
  font-weight: 400;
}

.progress p {
  font-size: 16px;
  line-height: inherit;
  color: #777777;
  font-weight: inherit;
  width: 100%;
  text-align: center;
  margin-top: 25px;
}

.progress.blue .progress-bar {
  border-color: var(--primary-color);
}

.progress.blue .progress-left .progress-bar {
  animation: loading-2 1.5s linear forwards 1.8s;
}

.progress.yellow .progress-bar {
  border-color: #fdba04;
}

.progress.yellow .progress-left .progress-bar {
  animation: loading-3 1s linear forwards 1.8s;
}

.progress.pink .progress-bar {
  border-color: #ed687c;
}

.progress.pink .progress-left .progress-bar {
  animation: loading-4 0.4s linear forwards 1.8s;
}

.progress.green .progress-bar {
  border-color: #1abc9c;
}

.progress.green .progress-left .progress-bar {
  animation: loading-5 1.2s linear forwards 1.8s;
}

@keyframes loading-1 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}
@keyframes loading-2 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(144deg);
    transform: rotate(144deg);
  }
}
@keyframes loading-3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}
@keyframes loading-4 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(36deg);
    transform: rotate(36deg);
  }
}
@keyframes loading-5 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(126deg);
    transform: rotate(126deg);
  }
}
@media only screen and (max-width: 990px) {
  .progress {
    margin-bottom: 20px;
  }
}
.conunter-img {
  margin-inline-start: -400px;
}

/* steps area */
.steps-area h2,
.steps-area h3 {
  color: #fff;
}

.steps-area li {
  margin-bottom: 30px;
}

.steps-area li:last-child {
  margin-bottom: 0;
}

.step-box {
  display: flex;
}

.step-box .text p {
  margin-bottom: 0;
  color: #9f9f9f;
}

.step-box .date-box {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  background-color: var(--primary-color);
  margin-inline-end: 30px;
  text-align: center;
  position: relative;
  float: left;
  color: #fff;
  font-family: var(--primary-font), sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 60px;
}

.step-box .date-box::before {
  content: "";
  width: 50px;
  height: 50px;
  inset-inline-start: 5px;
  border: 2px dashed #fff;
  position: absolute;
  top: 5px;
  border-radius: 50%;
}

.step-img {
  margin-inline-start: -305px;
}

/* Classes */
.class-item {
  float: left;
  margin-inline-start: 15px;
  margin-inline-end: 15px;
  box-shadow: 1px 1.732px 18px 0px rgba(0, 0, 0, 0.05);
  position: relative;
  background: #fff;
  border-radius: 4px;
}

.class-item::after {
  position: absolute;
  width: 80%;
  height: 20px;
  background: var(--primary-color);
  content: "";
  bottom: -21px;
  inset-inline-start: 10%;
  border-radius: 0 0 4px 4px;
  transform: translate(0, -103%);
  z-index: -1;
  transition: all 0.3s ease 0s;
}

.class-item:hover::after {
  transform: translate(0, 0%);
}

.class-img-outer {
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

.class-content {
  padding: 60px 40px 25px;
  float: left;
  position: relative;
}

.class-content h4 {
  font-size: 26px;
  margin-bottom: 15px;
}

.class-content .schedule span {
  display: block;
  line-height: 22px;
  color: #12265a;
}

.class-content .schedule li {
  float: left;
  padding: 0 15px;
  border-inline-end: 1px solid #ccc;
}

.class-content span.class-price {
  background: var(--primary-color);
  font-size: 20px;
  color: #fff;
  padding: 7px 10px;
  border-radius: 30px;
  width: 98px;
  height: 34px;
  text-align: center;
  font-weight: 700;
  font-family: var(--primary-font), sans-serif;
  margin-top: 5px;
}

.class-tech {
  display: inline-block;
  width: 180px;
  background: var(--primary-color);
  color: #fff;
  padding: 5px;
  border-radius: 30px;
  position: absolute;
  inset-inline-start: 25%;
  top: -25px;
}

.class-tech .img {
  float: left;
  margin-inline-end: 10px;
}

.class-tech .text {
  padding-top: 5px;
}

.class-content .schedule span.class-size {
  color: var(--primary-color);
}

.class-content .schedule li:first-child {
  padding-inline-start: 0;
}

.class-content .schedule li:last-child {
  padding-inline-end: 0;
  border-inline-end: none;
}

.course-widget-price ul {
  width: 100%;
  display: inline-block;
}

.course-widget-price li i {
  padding-inline-end: 9px;
  font-size: 14px;
  padding-inline-start: 2px;
  color: var(--primary-color);
}

.course-widget-price li span:last-child {
  float: right;
}

.class-area2 h2 {
  color: #fff;
}

.class-active .single-project {
  position: relative;
}

.class-active .slick-arrow {
  position: absolute;
  top: 30%;
  inset-inline-start: -60px;
  border: none;
  background: none;
  padding: 0;
  font-size: 36px;
  color: #fff;
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
  background: var(--primary-color);
  width: 60px;
  height: 60px;
  border-radius: 4px;
}

.class-active .slick-arrow:hover {
  background: var(--primary-color);
  color: #fff;
}

.class-active .slick-next {
  inset-inline-start: inherit;
  inset-inline-end: -60px;
}

.sidebar-widget a {
  color: #777;
}

.tag-cloud-link {
  font-size: 14px !important;
  border: 1px solid var(--primary-color);
  padding: 10px 19px !important;
  display: inline-block;
  margin: 5px 5px !important;
  text-transform: capitalize;
  float: none !important;
  width: auto !important;
}

.tag-cloud-link:hover {
  border: 1px solid var(--primary-color);
  color: #6a6a6a;
}

.widget_text img {
  height: auto;
}

.sidebar-widget select {
  width: 100%;
  padding: 3px 0;
  border: 2px solid #e4e4e4;
}

.recentcomments a {
  color: #101010;
}

.sidebar-widget a.rsswidget {
  color: #101010;
}

#wp-calendar caption {
  color: #101010;
  font-weight: 500;
  font-size: 14px;
}

.quote-post {
  background: url(../images/quote_bg.png);
}

.widget_media_image a:hover {
  padding-inline-start: 0 !important;
}

.sidebar-widget .widget li {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 5px;
  float: left;
  width: 100%;
  min-height: 42px;
}

.sidebar-widget .widget a {
  width: 100%;
  text-align: start;
  line-height: 28px;
  margin-bottom: -26px;
  position: relative;
  z-index: 1;
  color: #101010;
}

.sidebar-widget .widget li .children {
  border-top: 1px solid #e4e4e4;
  margin-top: 10px;
  padding-bottom: 0px;
  display: inline-block;
  width: 100%;
}

.sidebar-widget .widget li.page_item_has_children {
  padding-bottom: 0;
}

.sidebar-widget .widget .children {
  padding-inline-start: 15px;
}

.sidebar-widget .widget .children ul.children {
  padding-inline-start: 30px;
}

.sidebar-widget .page_item_has_children li {
  padding-top: 10px;
  display: block;
}

.sidebar-widget .widget .children li {
  padding-top: 10px;
}

.sidebar-widget .widget .children .children li {
  padding-top: 10px;
}

.sidebar-widget .widget li:last-child,
.sidebar-widget .widget .children li:last-child,
.sidebar-widget .widget .children .children li:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.sticky .bsingle__content,
.tag-sticky-2 {
  background: #e4e4e4;
}

.sticky .bsingle__content .meta-info ul {
  padding-top: 40px;
  border-top: 2px solid #e8e8e8;
  display: inline-block;
  width: 100%;
  padding-inline-start: 0;
}

.sticky .bsingle__content ul li {
  margin-bottom: 0;
  list-style: disc;
  margin-inline-start: 0;
  border-color: #e8e8e8;
}

#attachment_907 {
  width: 100% !important;
}

.wp-image-907 {
  width: 100% !important;
  height: auto;
}

.sidebar-widget .widget a {
  width: 85%;
  text-align: start;
}

.sidebar-widget .widget a:hover {
  padding-inline-start: 10px;
}

.sidebar-widget .widget .widget_archive li:hover,
.widget_categories li:hover,
.sidebar-widget .widget_archive li:hover {
  color: var(--primary-color);
}

.post-password-form input[type=password] {
  background: #e4e4e4;
  border: none;
  width: 300px;
  padding: 10px 30px;
}

.post-password-form input[type=submit] {
  border: none;
  background: #101010;
  padding: 10px 30px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

.post-password-form input[type=submit]:hover {
  background: var(--primary-color);
}

.wp-block-cover__video-background {
  width: 100%;
}

.sidebar-widget .widget_nav_menu .sub-menu {
  padding-inline-start: 15px;
}

.sidebar-widget .widget_nav_menu .sub-menu {
  display: block;
  position: relative;
  margin-top: 5px !important;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 1px solid #ddd;
}

.with-avatar .avatar {
  border-radius: 50% !important;
  display: none !important;
}

.ab-submenu .ab-item img {
  display: none !important;
}

.page #comments {
  float: left;
  width: 100%;
  margin-top: 50px;
}

video {
  width: 100%;
}

.sidebar-widget .widget_text a:hover {
  padding-inline-start: 0;
}

.dsnone {
  display: none;
}

.wp-block-media-text__media img {
  width: 100%;
}

.wp-block-image .alignleft {
  float: left;
  margin-inline-end: 1em;
}

.wp-block-image figure.alignright {
  margin-inline-start: 1.5em;
}

.wp-block-image .alignright {
  float: right;
  margin-inline-start: 1em;
}

.wp-block-image img {
  max-width: 100%;
}

.wp-block-image:not(.is-style-rounded) img {
  border-radius: inherit;
  height: auto;
}

.wp-block-columns.alignwide .wp-block-column {
  margin-inline-start: 0;
}

.wp-block-column .wp-block-quote {
  margin-top: 0;
  margin-inline-end: -15px;
  float: right;
}

.wp-block-columns.alignwide .wp-block-column {
  margin-inline-start: 0;
  padding-inline-end: 15px;
}

.wp-block-gallery.columns-2 li {
  width: 48%;
  padding: 10px;
}

.blocks-gallery-item img {
  margin: 0 1em 1em 0;
  margin-inline-end: 1em;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  justify-content: center;
  position: relative;
  align-self: flex-start;
  width: calc(50% - 1em);
}

.wp-block-image figcaption {
  margin-top: 0.5em;
  margin-bottom: 1em;
  font-weight: 400;
  color: #777;
}

.wp-block-galler {
  overflow: hidden;
  display: inline-block;
  position: relative;
  width: 100%;
}

.blocks-gallery-item {
  margin: 0;
  position: relative;
}

.blocks-gallery-item img,
.blocks-gallery-item img:hover {
  background: 0 0;
  border: none;
  box-shadow: none;
  max-width: 100%;
  padding: 0;
  vertical-align: middle;
}

.blocks-gallery-item figcaption {
  background: #eee;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 13px;
  font-weight: 400;
  overflow: hidden;
  padding: 10px 0;
  position: absolute;
  bottom: 0;
  text-indent: 10px;
  text-overflow: ellipsis;
  width: 100%;
  white-space: nowrap;
  opacity: 0;
}

.blocks-gallery-item:hover figcaption {
  opacity: 1;
}

.wp-block-gallery.columns-3 li {
  width: 33.333%;
  padding: 10px;
  height: 200px;
}

.wp-block-gallery.columns-2 li {
  width: 48%;
  padding: 10px;
}

.wp-block-gallery {
  width: 100%;
  float: left;
}

.wp-block-gallery.columns-5 li {
  width: auto;
  height: auto;
  overflow: hidden;
  columns: auto;
}

.bsingle__content .alignright {
  float: right;
  margin-inline-start: 1.5em;
}

.bsingle__content .alignleft {
  float: left;
  margin-inline-end: 1em;
}

.alignright {
  float: right;
  margin-inline-start: 1.5em;
}

.alignleft {
  float: left;
  margin-inline-end: 1em;
}

figure.aligncenter {
  width: 100% !important;
  text-align: center;
  float: left;
}

.aligncenter {
  margin: auto;
  display: inherit;
}

.page-links {
  float: left;
  width: 100%;
  margin-top: 50px;
}

#comments {
  float: left;
  width: 100%;
}

.bsingle__content ul,
.pages-content ul {
  padding-inline-start: 17px;
}

.comment-text ul {
  padding-inline-start: 20px;
}

.bsingle__content ul li,
.comment-text ul li,
.pages-content ul li {
  list-style: disc;
}

.bsingle__content table th,
.bsingle__content table td,
.comment-text table th,
.comment-text table td,
.pages-content table th,
.pages-content table td {
  border: 1px solid #e4e4e4;
  padding: 10px;
}

.bsingle__content table,
.comment-text table,
.pages-content table {
  margin-bottom: 20px;
}

.wp-block-media-text.alignfull.has-media-on-the-right.is-stacked-on-mobile {
  padding: 30px;
}

.bsingle__content ol {
  padding: 0 0 0 1.5em;
}

.bsingle__content ol li,
.comment-text ol li,
.pages-content ol li {
  list-style: decimal;
}

.bsingle__content figure {
  margin: 0 0 1rem;
}

a,
h1,
h2,
h3,
h4,
h5,
h6,
p,
span {
  overflow-wrap: break-word;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

.has-text-color.has-background.has-very-light-gray-color {
  color: #fff;
  padding: 21px;
}

.wp-block-cover-text {
  color: #fff !important;
  padding: 30px;
}

.inner-linke-page a,
.post-page-numbers {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  background: #c5015f;
  border-radius: 50%;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.inner-linke-page a:hover,
.post-page-numbers:hover {
  color: #ffffff;
  background: #010f2e;
}

.inner-linke-page > span,
.post-page-numbers.current {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  font-weight: 400;
  border-radius: 50%;
  font-size: 14px;
  text-align: center;
  background: #010f2e;
  color: #ffffff;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.32);
}

.tag-template img {
  width: auto !important;
}

.error-page .error-code {
  display: block;
  font-size: 150px;
  line-height: 150px;
  color: #333;
  margin-bottom: 20px;
  text-shadow: 5px 5px 1px rgba(0, 0, 0, 0.1);
}

.error-body a {
  margin-top: 30px;
  margin-bottom: 100px;
}

.tag-markup-2 li > ul,
li > ol,
.pages-content li > ul,
li > ol {
  padding-inline-start: 1.5em;
}

.tag-markup-2 ul li,
.tag-markup-2 ol li,
.comment-text ul li,
.comment-text ol li,
.pages-content ul li,
.pages-content ol li {
  margin-top: 10px;
  margin-bottom: 0;
}

.parent-pageid-1725 figure.aligncenter {
  margin: 50px 0;
}

.bsingle__content ul ul li,
.pages-content ul ul li {
  list-style: circle;
}

footer #wp-calendar caption {
  color: #fff;
}

ol {
  padding-inline-start: 15px;
}

iframe {
  width: 100%;
}

.comment-list li {
  margin-bottom: 30px;
}

.wp-block-button__link {
  border: none;
  font-weight: 700;
  padding: 0.76rem 1rem;
  outline: none;
  outline: none;
  display: inline-block;
  background: #101010;
  color: #fff !important;
  width: auto;
  border-radius: 5px;
  text-align: center;
  margin-bottom: 15px;
}

.wp-block-button.aligncenter .wp-block-button__link {
  width: 50%;
  display: block;
  margin: auto;
}

.wp-block-button.is-style-outline .wp-block-button__link {
  background: none;
  border: 2px solid #101010;
  color: #101010 !important;
}

.wp-block-button.is-style-squared .wp-block-button__link {
  border-radius: 0;
}

.has-2-columns,
.has-3-columns,
.has-5-columns,
.has-6-columns {
  float: left;
  width: 100%;
}

.has-2-columns .wp-block-column {
  float: left;
  width: 50%;
  padding-inline-end: 20px;
}

.has-3-columns .wp-block-column {
  float: left;
  width: 33.333%;
  padding-inline-end: 20px;
}

.has-5-columns .wp-block-column {
  float: left;
  width: 20%;
  padding-inline-end: 20px;
}

.has-4-columns .wp-block-column {
  float: left;
  width: 25%;
  padding-inline-end: 20px;
}

.has-6-columns .wp-block-column {
  float: left;
  width: 16.6666666667%;
  padding-inline-end: 20px;
}

.wp-block-media-text.alignwide figure {
  float: left;
  width: 100%;
  margin-inline-end: 20px;
}

.wp-block-media-text.alignwide .has-large-font-size {
  font-size: 28px;
  color: #101010;
}

.wp-block-media-text__content {
  float: left;
  width: 47%;
  padding-top: 0;
}

.wp-block-columns.alignwide.has-3-columns blockquote {
  margin-top: 0;
  padding-inline-start: 20px;
}

.columns-3.is-cropped li {
  list-style: none;
  float: left;
  width: 33.333%;
}

.wp-block-gallery.columns-3.is-cropped.alignfull {
  margin-top: 0;
}

.blocks-gallery-grid,
.wp-block-gallery {
  padding: 0 !important;
}

.wp-block-gallery.alignleft.columns-2.is-cropped li {
  float: left;
  list-style: none;
}

.wp-block-gallery.alignwide.columns-4.is-cropped li {
  float: left;
  list-style: none;
  width: 25%;
  padding: 10px;
}

.wp-block-gallery.columns-6.is-cropped li {
  float: left;
  width: 16.666%;
  height: 129px;
  list-style: none;
  padding: 10px;
}

.wp-block-gallery.columns-7.is-cropped li {
  float: left;
  width: 14%;
  padding: 10px;
  list-style: none;
  min-height: 150px;
}

.wp-block-gallery.columns-8.is-cropped li {
  float: left;
  width: 12%;
  padding: 10px;
  list-style: none;
  min-height: 100px;
}

.wp-block-media-text.alignfull.has-media-on-the-right.is-stacked-on-mobile {
  float: left;
}

.wp-block-media-text.alignfull.has-media-on-the-right.is-stacked-on-mobile figure {
  float: left;
  width: 50%;
  margin-inline-end: 20px;
}

.footer-widget .widgettitle,
.footer-widget .cat-item,
.footer-widget .widget ul li {
  text-align: start;
  color: #fff;
}

.blog-deatails-box.single .single {
  display: none;
}

.redux-footer h2 {
  margin-top: 0px;
}

.footer-widget td {
  padding: 5px;
  text-align: center;
}

.footer-widget table {
  width: 100%;
  padding: 10px;
  background: #f5f5f5;
}

.footer-widget th {
  background: #e4e4e4;
  padding: 10px 5px;
  text-align: center;
  color: #101010;
}

.bsingle__content h1,
.bsingle__content h2,
.bsingle__content h3,
.bsingle__content h4,
.bsingle__content h5,
.bsingle__content h6 {
  line-height: 1.3;
  margin-bottom: 20px;
  font-weight: 700;
}

.pages-content h1,
.pages-content h2,
.pages-content h3,
.pages-content h4,
.pages-content h5,
.pages-content h6 {
  line-height: 1.3;
  margin-bottom: 20px;
  font-weight: 700;
}

.comment-list h1,
.comment-list h2,
.comment-list h3,
.comment-list h4,
.comment-list h5,
.comment-list h6 {
  line-height: 1.3;
  margin-bottom: 20px;
  font-weight: 700;
}

.inner-linke-page {
  font-size: 16px;
  font-weight: 600;
  color: #101010;
}

.inner-linke-page a {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.inner-linke-page a:hover {
  color: #ffffff;
  background: #101010;
}

.inner-linke-page > span {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  font-weight: 400;
  border-radius: 50%;
  font-size: 14px;
  text-align: center;
  background: #101010;
  color: #ffffff;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.32);
}

pre {
  padding: 30px;
  background: #e4e4e4;
}

pre cite {
  color: #000;
}

.inner-linke-page {
  margin-bottom: 50px;
}

.attachment-aports-featured-large {
  width: 100%;
  height: auto;
}

.quote-post {
  min-height: 200px;
}

.admin-bar .sticky-menu {
  top: 32px;
  margin-top: 0;
}

.f-contact .icon {
  margin-top: unset;
}
.f-contact ul li {
  display: flex;
  align-items: center;
}

.author-blog-avatar {
  width: 120px;
  height: 120px;
}

.custom-blog-tag-sidebar:hover {
  background-color: var(--primary-color) !important;
  color: #fff !important;
}

.custom-blog-post-sidebar {
  padding: 40px;
  overflow: hidden;
  margin-bottom: 40px;
  border: 2px solid #f7f5f1 !important;
  background: #f7f5f1;
}
.custom-blog-post-sidebar a {
  font-weight: bold;
  line-height: 28px;
  color: #101010;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.custom-blog-post-sidebar a:hover {
  color: var(--primary-color) !important;
}
.custom-blog-post-sidebar li {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 5px;
  width: 100%;
  min-height: 42px;
  color: #101010;
}

.show-admin-bar .sticky-menu {
  margin-top: 40px !important;
}
.show-admin-bar .offcanvas-menu {
  margin-top: 40px;
}
.show-admin-bar .side-menu .menu-mobile {
  top: 130px;
}

.custom-gallery-description {
  font-size: 20px;
}
.custom-gallery-description:first-letter {
  color: var(--primary-color);
  font-size: 28px;
}

.single-services {
  margin: 0 15px;
}
.single-services .icon li img {
  height: 30px;
}

ul.room-features li img {
  width: 20px;
  margin-inline-end: 8px;
}

.contact-field label i {
  margin-inline-end: 10px;
}

.customer-help {
  width: 100%;
  margin-inline-start: 10%;
  margin-top: 10px;
  font-size: 16px;
}
.customer-help span:first-of-type {
  display: inline-block;
  width: 100px;
  margin-inline-start: 8px;
  margin-inline-end: 16px;
}

.collection-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.customer-avatar-header {
  width: 30px;
}

.customer-name-header {
  font-size: 16px;
}
.customer-name-header:hover {
  color: var(--secondary-color) !important;
  transition: 0.3s ease;
}

.customer-name-canvas:hover {
  color: var(--primary-color) !important;
  transition: 0.3s ease;
}

.cb-container {
  cursor: pointer;
  display: block;
  line-height: 25px;
  margin-bottom: 12px;
  padding-inline-start: 34px;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.cb-container input {
  cursor: pointer;
  height: 0;
  opacity: 0;
  position: absolute;
  width: 0;
}

.checkmark {
  background-color: #fff;
  border: 2px solid #8ea4ac;
  border-radius: 4px;
  height: 24px;
  inset-inline-start: 0;
  position: absolute;
  top: 1px;
  width: 24px;
}

.cb-container input:checked ~ .checkmark {
  border: 2px solid #8ea4ac;
}

.cb-container .text-small {
  color: #3d565f;
  font-weight: 500;
}

.cb-container input:checked ~ .text-small {
  color: #3d565f;
}

.checkmark:after {
  content: "";
  display: none;
  position: absolute;
}

.cb-container input:checked ~ .checkmark:after {
  display: block;
}

.cb-container input:checked ~ .text-lbl {
  color: #ccc;
}

.cb-container .checkmark:after {
  left: -2px;
  top: -2px;
  width: 24px;
  height: 24px;
  background: var(--primary-color) url(/themes/riorelax/images/check.svg) no-repeat center;
  border-radius: 4px;
}

@media screen and (min-width: 1500px) {
  .booking-area.homepage .contact-form {
    width: 75%;
    margin: -100px auto 0 auto;
  }
}
.booking-area.homepage .contact-form .slider-btn {
  margin-top: 14px;
  width: 100%;
}
@media screen and (max-width: 991px) {
  .booking-area.homepage .contact-form .slider-btn .label {
    text-align: unset;
  }
}
@media screen and (max-width: 1500px) and (min-width: 1200px) {
  .booking-area.homepage .contact-form .slider-btn {
    margin-top: unset;
  }
}
.booking-area.homepage .contact-form .btn {
  width: 100%;
  padding: 21.5px 20px;
}
@media screen and (max-width: 991px) {
  .booking-area.homepage .contact-form .btn {
    font-size: 16px;
    margin-top: 1px;
  }
}
.booking-area.homepage .contact-form ul li {
  width: unset;
}

.widget-content .booking .slider-btn {
  width: 100%;
}
.widget-content .booking .slider-btn .btn {
  width: 100%;
}
.widget-content .booking .contact-field select {
  height: calc(3em + 0.75rem + 2px);
}

.custom-authentication-label {
  font-size: 18px !important;
  margin-bottom: 5px !important;
}

.custom-authentication-input {
  height: calc(2em + 0.5rem + 2px) !important;
}

.custom-register-label {
  color: var(--primary-color);
  font-weight: bold;
}

.checkout-booking-page .payment-checkout-form {
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.07);
}
.checkout-booking-page .checkout-booking .sidebar .wrap {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding-top: 75%;
}
.checkout-booking-page .checkout-booking .sidebar .wrap img {
  position: absolute;
  inset: 0;
  height: auto;
  width: 100%;
  margin: auto;
}
.checkout-booking-page .checkout-booking .sidebar .wrap .room-information {
  position: absolute;
  top: 5%;
}
.checkout-booking-page .checkout-booking .sidebar .wrap .room-information span {
  padding: 10px;
  background-color: var(--primary-color);
  color: white;
}
.checkout-booking-page .checkout-booking .sidebar .form-information {
  background-color: var(--primary-color);
  color: white;
  padding: 20px 15px;
}
.checkout-booking-page .checkout-booking .sidebar .form-information div {
  margin: 0 20px;
}
.checkout-booking-page .checkout-booking .sidebar .form-information div p {
  margin-bottom: 5px;
  font-size: 14px;
  color: #dedcdc;
}
.checkout-booking-page .checkout-booking .sidebar .footer {
  background-color: black;
  color: white;
}
.checkout-booking-page .checkout-booking .sidebar .footer p {
  padding: 20px 15px;
  font-size: 24px;
  font-weight: 500;
}
.checkout-booking-page .checkout-booking .booking-form-main {
  background-color: #fff;
  padding: 30px;
}
.checkout-booking-page .checkout-booking .widget-content {
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.07);
  background-color: #fff;
  padding: 40px 50px;
  margin-bottom: 50px;
}
.checkout-booking-page .checkout-booking .widget-content h3 {
  border-bottom: 1px solid #f1f1f1;
  font-size: 30px;
  padding-bottom: 20px;
}
.checkout-booking-page .checkout-booking .widget-content.hotel-rules ul li {
  position: relative;
  margin-bottom: 10px;
  padding-inline-start: 20px;
}
.checkout-booking-page .checkout-booking .widget-content.hotel-rules ul li:before {
  color: var(--primary-color);
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.booking-information-page {
  background-color: #f8f8f8;
}
.booking-information-page .booking-information {
  background-color: #fff;
  padding: 30px;
}

@media (min-width: 1500px) and (max-width: 1742px) {
  .header-social {
    margin-top: unset !important;
  }
}
.header-social a span {
  font-size: 15px;
}

.currencies-switcher a.dropdown-toggle img, .language-switcher a.dropdown-toggle img {
  margin-bottom: 3px;
}
.currencies-switcher .dropdown-menu, .language-switcher .dropdown-menu {
  padding: unset;
}
.currencies-switcher a, .language-switcher a {
  font-size: 15px;
}
.currencies-switcher .language-switcher-list a, .currencies-switcher .currency-switcher-list a, .language-switcher .language-switcher-list a, .language-switcher .currency-switcher-list a {
  padding: 10px 20px;
}
.currencies-switcher li a.language-item, .language-switcher li a.language-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.currencies-switcher li a.language-item span, .language-switcher li a.language-item span {
  color: black;
  margin-inline-start: 4px;
}
.currencies-switcher li a.language-item:hover span, .language-switcher li a.language-item:hover span {
  color: var(--primary);
}
.currencies-switcher li a, .language-switcher li a {
  width: 100%;
  color: #333;
  display: block;
  float: left;
  margin: 0;
  text-align: left;
  text-decoration: none;
  font-size: 15px;
  font-weight: 400;
}
.currencies-switcher li:not(:last-child) a, .language-switcher li:not(:last-child) a {
  border-bottom: 1px solid rgba(217, 217, 217, 0.5);
}
.currencies-switcher .currency-switcher-list, .currencies-switcher .language-switcher-list, .language-switcher .currency-switcher-list, .language-switcher .language-switcher-list {
  transform: translate(15px, 35px) !important;
}

.second-header {
  z-index: 10;
}

.footer-bg .footer-top {
  background-color: black;
}

.button-loading {
  opacity: 0.7;
  color: transparent !important;
  cursor: default;
  position: relative;
  text-shadow: none;
  transition: border-color 0.2s ease-out;
}
.button-loading:before {
  animation: button-loading-spinner 1s linear infinite;
  border: 3px solid #ffffff;
  border-bottom-color: transparent;
  border-radius: 50%;
  content: "";
  height: 18px;
  left: 50%;
  margin-inline-start: -9px;
  margin-top: -9px;
  position: absolute;
  top: 50%;
  width: 18px;
}
@keyframes button-loading-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.btn:before {
  content: none !important;
}
.btn.button-loading:before {
  content: "" !important;
  display: block !important;
}

form.form-booking #booking-form-widget-check-in {
  font-size: unset;
  color: black !important;
}

.booking-area .contact-field i {
  padding-inline-end: unset;
}

@media (min-width: 768px) and (max-width: 991px) {
  .header-three .second-header {
    display: block !important;
  }
  .header-three .second-header .header-top-left, .header-three .second-header .header-top-right {
    display: block !important;
  }
}
@media screen and (max-width: 991px) {
  .header-top .header-top-left .opening_hours {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .header-top .header-top-right .social-links {
    display: none;
  }
}

.mean-nav .main-menu .language-switcher-mobile-menu, .mean-nav .main-menu .currency-switcher-mobile-menu {
  display: block !important;
}
.mean-nav .main-menu .language-switcher-mobile-menu a, .mean-nav .main-menu .currency-switcher-mobile-menu a {
  display: flex;
  align-items: center;
}
.mean-nav .main-menu .language-switcher-mobile-menu a img, .mean-nav .main-menu .currency-switcher-mobile-menu a img {
  margin-top: -5px;
}
.mean-nav .main-menu .language-switcher-mobile-menu a span, .mean-nav .main-menu .currency-switcher-mobile-menu a span {
  margin-inline-start: 10px;
}
.mean-nav .main-menu .language-switcher-mobile-menu:not(:last-child) a, .mean-nav .main-menu .currency-switcher-mobile-menu:not(:last-child) a {
  border-bottom: 1px solid rgba(217, 217, 217, 0.5);
}

.custom-pagination {
  display: flex;
}
.custom-pagination .page-item a.page-link {
  font-size: 16px;
  border: 1px solid #acacac;
  border-radius: 10px;
  background-color: #fff;
  color: #acacac;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}
.custom-pagination .page-item a.page-link:hover {
  color: #fff;
  border-color: #6bb2ff;
  background-color: #6bb2ff;
  box-shadow: none;
}
.custom-pagination .page-item a[rel=prev], .custom-pagination .page-item a[rel=next] {
  font-size: 20px;
}
.custom-pagination .page-item.active span.page-link {
  padding: 0;
  line-height: 40px;
  font-size: 16px !important;
  border: 1px solid #6bb2ff !important;
  border-radius: 10px !important;
  background-color: #fff !important;
  color: #6bb2ff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}
.custom-pagination .page-item.active span.page-link:hover {
  color: #6bb2ff;
  border-color: #6bb2ff;
  background-color: #fff;
  box-shadow: none;
}
.custom-pagination .disabled span.page-link {
  font-size: 32px;
  border: 1px solid #acacac;
  border-radius: 10px;
  background-color: #fff;
  color: #acacac;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}
.custom-pagination .disabled span.page-link-navigation {
  font-size: 20px;
}

.custom-booking-table thead th {
  font-size: 18px;
}
.custom-booking-table .room-table {
  color: var(--primary-color);
}
.custom-booking-table .room-table:hover {
  text-decoration: underline;
}

.booking-information-link {
  color: var(--primary-color);
}
.booking-information-link:hover {
  text-decoration: underline;
}

.custom-login-button {
  height: 28px;
}
.custom-login-button i, .custom-login-button span {
  line-height: 28px;
  padding: 0 4px;
}
.custom-login-button i:hover, .custom-login-button span:hover {
  color: var(--primary-color) !important;
}
.custom-login-button span {
  margin: 0;
  font-weight: 700;
}

.custom-avatar-master {
  position: relative !important;
  display: flex;
  margin: 0 auto;
  width: 150px;
}
.custom-avatar-master:hover {
  background-color: #aeaeae;
  border-radius: 999px;
}
.custom-avatar-master:hover .mt-card-avatar {
  opacity: 0.5;
}
.custom-avatar-master i {
  font-size: 20px;
  color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}
.custom-avatar-master i:hover {
  opacity: 1;
}
.custom-avatar-master .avatar-view:hover ~ i {
  opacity: 1;
}

.panel.panel-default {
  overflow: hidden;
}

.custom-search-form {
  display: flex;
}
.custom-search-form .search-submit {
  position: static;
}

.custom-link {
  color: var(--primary-color);
}
.custom-link:hover {
  color: var(--secondary-color);
}

body[dir=rtl] .gallery-image:before {
  inset-inline-start: 38%;
}
body[dir=rtl] .faq-wrap .card-header h2 button:before {
  transform: rotateY(180deg);
}
body[dir=rtl] .meanmenu-reveal {
  left: 0 !important;
  right: auto !important;
}
@media (max-width: 992px) {
  body[dir=rtl] .mean-container .mean-nav ul li a {
    text-align: start;
  }
  body[dir=rtl] .mean-container .mean-nav ul li a.mean-expand {
    left: 0;
    right: auto;
    text-align: end;
  }
}
body[dir=rtl] .pricing-btn .btn i {
  transform: rotateY(180deg);
}
body[dir=rtl] .pricing-body li:before {
  float: right;
}
body[dir=rtl] .pricing-head .month {
  transform: rotate(270deg);
}
body[dir=rtl] .services-08-content a i {
  transform: rotateY(180deg);
}
body[dir=rtl] .header-slidemenu {
  left: auto;
  right: 0;
}
body[dir=rtl] .offcanvas-menu {
  transform: translateX(-100%);
}
body[dir=rtl] .offcanvas-menu.active {
  transform: translateX(0);
}

.faq-btn {
  position: relative;
}

button.faq-btn.collapsed {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-item-type-custom:hover ~ a {
  color: var(--primary-color) !important;
}

.team-social i:hover {
  color: var(--secondary-color) !important;
}

button.btn-custom:hover {
  background-color: var(--primary-color-hover) !important;
  transition: 0.3s;
}

.room-item-custom-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-item-custom-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.error-page {
  background-color: #F7F5F1;
}

.menu-mobile {
  position: absolute;
  top: 80px;
  width: 100%;
  z-index: 10;
}
.menu-mobile .navbar-collapse {
  margin: 0 10%;
  background: white;
}
.menu-mobile .navbar-collapse ul li a {
  width: 100%;
  color: #333;
  display: block;
  float: left;
  margin: 0;
  padding: 10px 5%;
  text-align: left;
  text-decoration: none;
  font-size: 15px;
  font-weight: 400;
}
.menu-mobile .navbar-collapse ul li:not(:last-child) a {
  border-bottom: 1px solid rgba(217, 217, 217, 0.5);
}
@media screen and (max-width: 600px) {
  .menu-mobile .navbar-collapse {
    margin: 0;
  }
}

.btn-toggle-menu-mobile {
  padding: 10px 10px !important;
  background-color: transparent;
  border: white 1px solid;
}
.btn-toggle-menu-mobile:hover {
  background-color: transparent;
}
.btn-toggle-menu-mobile i {
  font-size: 20px;
  margin-inline-start: unset;
}

.sticky-menu .menu-mobile {
  top: 73px;
}

.show-admin-bar .menu-mobile {
  top: 80px;
}
.show-admin-bar .sticky-menu .menu-mobile {
  top: 73px;
}
.show-admin-bar .side-menu .menu-mobile {
  top: 100px;
}

#menu-mobile-nav {
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.08);
}
#menu-mobile-nav .menu {
  padding: 20px 10px;
  max-height: calc(100vh - 150px);
  overflow: auto;
}
#menu-mobile-nav .menu .menu-title {
  margin-bottom: 10px;
  margin-inline-start: 10px;
}
#menu-mobile-nav .menu .menu-title span {
  font-size: 20px;
  font-weight: 500;
  color: var(--primary-color);
  padding-bottom: 5px;
  padding-left: 4px;
  padding-inline-start: 4px;
  border-bottom: 3px var(--primary-color) solid;
}
#menu-mobile-nav ul li {
  position: relative;
}
#menu-mobile-nav ul li a {
  color: #777;
}
#menu-mobile-nav ul li a.has-sub {
  color: #333;
}
#menu-mobile-nav ul li a.active {
  color: var(--primary-color);
  font-weight: 500;
}
#menu-mobile-nav ul li a.collapsed.has-sub:after {
  content: "+";
  font-size: 18px;
  position: absolute;
  right: 0;
}
#menu-mobile-nav ul li a.has-sub:after {
  content: "-";
  font-size: 18px;
  position: absolute;
  right: 0;
}

.button-switch-currency {
  text-transform: unset;
}

.hotel-rules-box {
  margin-bottom: 30px;
}
.hotel-rules-box ul li {
  position: relative;
  margin-bottom: 10px;
  padding-inline-start: 20px;
}
.hotel-rules-box ul li:before {
  color: var(--primary-color);
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  font-weight: 300;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.room-block-content {
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.07);
  padding: 40px 50px;
  margin-bottom: 50px;
}
.room-block-content h3 {
  font-size: 30px !important;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 20px;
}

.room-details .thumb {
  margin-bottom: 40px;
}
.room-details .thumb img {
  border-radius: 2px;
}
.room-details .thumb .room-details-slider-nav {
  margin-top: 12px;
}
.room-details .thumb .room-details-slider-nav .slick-list .slick-track {
  margin-inline-start: unset;
  margin-inline-end: unset;
}
.room-details .thumb .room-details-slider-nav img {
  width: 130px !important;
  margin-inline-end: 8px;
}
.room-details .thumb .room-details-slider-nav img:last-child {
  margin: 0;
}

select {
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.related-room.content-box h3 {
  font-size: 30px !important;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 20px;
  margin-bottom: 30px !important;
}
.related-room.content-box .single-services {
  margin: unset;
}

.book-button-custom {
  color: #fff;
  background-color: transparent;
  border: none;
  font-weight: 600;
  padding: 20px 10px;
}
.book-button-custom:hover {
  color: var(--secondary-color);
  transition: 0.3s ease;
}

.check-availability-custom {
  margin-top: 75px;
  background: #f3f4f8;
  padding: 40px;
}

.shadow-block {
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) and (max-width: 991px) {
  .team-img-box {
    width: 70%;
    margin-inline-start: 15%;
  }
}
.team-img-box img {
  width: 100%;
}

.amenities-list span {
  color: #101010;
  font-family: var(--primary-font), sans-serif;
  font-size: 18px;
  font-weight: 400;
}

.widget-social a {
  display: inline-block;
  margin-bottom: 8px !important;
}

.blog-area .bsingle__content {
  border: 1px solid #b7b7b7;
}

.reviews-block {
  position: relative;
}

.custom-submit-review-btn {
  border: none;
  height: 50px;
  min-width: 160px;
  color: #fff;
  background-color: var(--primary-color);
}
.custom-submit-review-btn:hover {
  background-color: var(--primary-color-hover);
}
.custom-submit-review-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
.custom-submit-review-btn:disabled:hover {
  opacity: 0.7;
}

.rating-wrap {
  font-family: "Font Awesome 5 Pro" !important;
  vertical-align: top;
  overflow: hidden;
  position: relative;
  height: 20px;
  width: 75px;
  display: inline-block;
}
.rating-wrap:before {
  font-size: 12px;
  content: "\f005\f005\f005\f005\f005";
  top: 0;
  position: absolute;
  left: 0;
  float: left;
  color: #d2d2d2;
  letter-spacing: 2px;
  font-weight: 600;
}
.rating-wrap .review-rate {
  overflow: hidden;
  font-family: "Font Awesome 5 Pro" !important;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
  color: #EDB867;
}
.rating-wrap .review-rate:before {
  font-size: 12px;
  content: "\f005\f005\f005\f005\f005";
  top: 0;
  position: absolute;
  left: 0;
  letter-spacing: 2px;
  font-weight: 600;
}

.reviews-list.blur {
  opacity: 0.3;
}

.custom-review-input {
  width: 100%;
  min-height: 120px;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 10px;
}
.custom-review-input:disabled {
  background-color: #dfdfdf;
}

.review-item-block {
  margin-top: 10px;
}
.review-item-block .img-block {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}
.review-item-block .img-block .review-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 999px;
}
.review-item-block .reviewer-name {
  display: inline-block;
  margin-top: 8px;
  margin-bottom: 4px;
  margin-inline-end: 8px;
  font-size: 18px;
  font-weight: 700;
}
.review-item-block .review-time {
  color: var(--primary-color);
}
.review-item-block .review-content {
  font-weight: 400;
  margin-bottom: 10px !important;
}

.loading-spinner {
  align-items: center;
  background: hsla(0, 0%, 100%, 0.5);
  display: flex;
  height: 100%;
  inset-inline-start: 0;
  justify-content: center;
  position: absolute;
  top: 0;
  width: 100%;
}
.loading-spinner:after {
  animation: lds-dual-ring 0.5s linear infinite;
  border-color: var(--primary-color) transparent var(--primary-color) transparent;
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  content: " ";
  display: block;
  height: 40px;
  position: absolute;
  top: 18rem;
  width: 40px;
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.review-information-link {
  color: var(--primary-color);
}
.review-information-link:hover {
  text-decoration: underline;
}

@keyframes load7 {
  0%, 80%, to {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.preloader .loader {
  animation-fill-mode: both;
  animation: load7 1.8s ease-in-out infinite;
  animation-delay: -0.16s;
  border-radius: 50%;
  color: var(--primary-color);
  font-size: 10px;
  height: 2em;
  margin: 80px auto;
  position: relative;
  text-indent: -9999em;
  transform: translateZ(0);
  width: 2em;
}
.preloader .loader:before {
  border-radius: 50%;
  content: "";
  height: 2em;
  position: absolute;
  top: 0;
  width: 2em;
}
.preloader .loader:after {
  border-radius: 50%;
  content: "";
  height: 2em;
  position: absolute;
  top: 0;
  width: 2em;
  animation: load7 1.8s ease-in-out infinite;
  left: 3.5em;
}

button.toggle-coupon-form {
  border: none;
  background: transparent;
  color: var(--secondary-color);
  transition: color 0.2s ease;
}
button.toggle-coupon-form:hover {
  color: var(--primary-color-hover);
}

button.remove-coupon-code {
  color: #fff;
}
button.remove-coupon-code i {
  margin-inline-start: 0 !important;
}

.main-menu .has-sub > ul > .has-sub > ul {
  margin-inline-start: 255px;
}

.sidebar.services-sidebar {
  display: flex;
  flex-direction: column;
  gap: 50px;
}
.sidebar.services-sidebar .sidebar-widget {
  margin-bottom: unset;
}
.sidebar.services-sidebar .service-detail-contact {
  margin-top: unset;
  margin-bottom: unset;
}

.datagrid {
  --bb-datagrid-padding: 1.5rem;
  --bb-datagrid-item-width: 15rem;
  display: grid;
  grid-gap: var(--bb-datagrid-padding);
  grid-template-columns: repeat(auto-fit, minmax(var(--bb-datagrid-item-width), 1fr));
}

.datagrid-title {
  font-size: 0.625rem;
  font-weight: var(--bb-font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.04em;
  line-height: 1rem;
  color: var(--bb-secondary);
  margin-bottom: 0.25rem;
}

.text-success-fg, .text-danger-fg, .text-warning-fg, .text-info-fg {
  color: #fff;
}

.booking-form-body h4 {
  margin-bottom: 20px;
}

.auth-card form .auth-input-icon {
  top: 0;
}

.contact-area .contact-form {
  text-align: start;
}
.contact-area .contact-form label.required:after {
  content: "*";
  color: #dc3545;
  margin-left: 0.25rem;
}
.contact-area .contact-form input.contact-form-input, .contact-area .contact-form input.form-control {
  border: 1px solid #777;
  border-radius: 0;
  height: calc(3em + 0.75rem + 2px);
  padding-inline-start: 15px;
  transition: 0.3s;
  width: 100%;
}
.contact-area .contact-form textarea.contact-form-input {
  border: 1px solid #777;
  transition: 0.3s;
  border-radius: 0;
}
.contact-area .contact-form button.contact-button {
  display: block;
  width: 100%;
  background-color: var(--primary-color);
  padding: 20px 30px;
  font-size: 16px;
  border-radius: 0;
  border: none;
  margin-top: 20px;
}
.contact-area .contact-form .form-check {
  display: flex;
  align-items: end;
  gap: 5px;
}
.contact-area .contact-form .form-check .form-check-input {
  width: 16px;
  height: 16px;
  padding-left: unset;
}
.contact-area .contact-form .form-check .form-check-label {
  line-height: 1;
}

.form-newsletter {
  height: 70px;
}
.form-newsletter form.subscribe-form {
  height: 100%;
}
.form-newsletter form.subscribe-form input.form-control {
  height: 4.4rem;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  border-radius: 0;
  margin-bottom: 0;
  outline: none !important;
  padding: 0 1rem;
  width: 100%;
}
.form-newsletter form.subscribe-form .btn.header-btn {
  z-index: 10;
}

@media screen and (max-width: 991px) {
  .contact-area .contact-form {
    padding: 0 20px;
  }
}
.hotel-service-area .service-item .service-price {
  color: var(--primary-color);
}

[data-bb-toggle=toggle-guests-and-rooms] {
  background-color: #ffffff !important;
  text-align: start;
  font-weight: inherit !important;
  font-size: 15px !important;
  display: flex;
  align-items: center;
  text-transform: initial !important;
  letter-spacing: initial !important;
  padding: 10px 20px;
  width: 100%;
  transition: 0.3s;
  border: 1px solid var(--input-border-color);
  height: calc(3em + 0.75rem + 2px);
}

#toggle-guests-and-rooms {
  margin-top: 2px;
}

.custom-dropdown {
  width: 18rem;
}
@media (max-width: 767px) {
  .custom-dropdown {
    width: 100%;
  }
}
.custom-dropdown .inputs-filed {
  justify-content: space-between;
  display: flex;
  align-items: center;
  gap: 2rem;
}
.custom-dropdown .inputs-filed .input-quantity {
  max-width: 10rem;
}
.custom-dropdown .inputs-filed .main-btn {
  height: 40px !important;
  width: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 20px !important;
}

.input-quantity {
  align-items: end;
  display: flex;
  width: 100%;
}
.input-quantity input {
  height: 40px !important;
  padding: unset !important;
  text-align: center;
  padding-left: 15px !important;
}

.sidebar-widget.categories.check-availability-custom .input-group-two .input-quantity input, .contact-field .input-group-two .input-quantity input {
  height: 56px !important;
}

.contact-field.input-group-two .input-quantity input {
  height: 56px !important;
}

@media screen and (min-width: 1500px) {
  .booking-area.homepage .contact-form {
    width: 90% !important;
  }
}
/* 4. about */
.s-about-img img {
  margin-bottom: 100px;
}

.about-icon {
  position: absolute;
  bottom: 0;
  inset-inline-end: 0;
}

.about-icon img {
  margin-bottom: 0;
}

.about-user {
  display: flex;
  margin: 30px 0;
  border-bottom: 1px solid #d7d7d7;
  padding-bottom: 15px;
}

.about-user .img {
  margin-inline-end: 30px;
}

.about-user .text h5 {
  color: var(--primary-color);
}

.about-text {
  position: absolute;
  bottom: 0px;
  inset-inline-end: 30px;
  width: 200px;
  background: var(--primary-color);
  padding: 30px;
  border-radius: 4px;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.08);
}

.about-text span {
  font-size: 65px;
  color: #fff;
  position: relative;
  display: inline-block;
  font-weight: 800;
  font-family: var(--primary-font), sans-serif;
  margin-top: 15px;
}

.about-text span sub {
  font-size: 30px;
  color: #fff;
  font-weight: 800;
  margin-inline-start: -10px;
  bottom: 5px;
}

.about-title > span {
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  color: #fff;
  background: var(--primary-color);
  height: 30px;
  display: inline-block;
  text-align: center;
  line-height: 30px;
  margin-bottom: 30px;
}

.about-title h2 {
  font-size: 60px;
  margin-bottom: 0 !important;
}

.about-title h5 {
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.about-title .title-strong {
  color: #4f4f4f;
  font-weight: 500;
  font-size: 18px;
}

.about-title .title-strong span {
  padding: 3px 12px;
  background: var(--primary-color);
  border-radius: 5px;
  color: #fff;
}

.about-title p span {
  display: inline-block;
  height: 2px;
  width: 40px;
  background: var(--primary-color);
  margin-inline-end: 20px;
  position: relative;
  top: -5px;
}

.about-title p {
  margin-bottom: 0;
}

.about-content p {
  margin-bottom: 45px;
}

.about-content li {
  display: flex;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.about-content li .text {
  padding-top: 0;
}

.about-title h2 span {
  color: var(--primary-color);
}

.about-content li .icon i {
  width: 40px;
  height: 40px;
  background: #ebebeb;
  text-align: center;
  line-height: 40px;
  margin-inline-end: 20px;
  color: var(--primary-color);
}

.product-qulity li h3 {
  font-size: 26px;
  font-weight: 700;
}

.product-qulity li .icon {
  width: 140px;
  margin-top: 12px;
}

.about-content .exprince {
  border-radius: 10px;
  background-color: rgb(255, 255, 255);
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.15);
  text-align: center;
  padding: 30px 0;
}

.about-content .exprince h5 {
  margin-top: 15px;
  margin-bottom: 0;
  font-size: 25px;
  font-weight: 500;
  color: #0595b8;
}

.about-content .exprince p {
  margin-bottom: 0;
}

.about-content .nav-tabs {
  border: 1px solid #101010;
  width: 54%;
  padding: 6px;
  display: inherit;
  border-radius: 10px;
  margin-bottom: 30px;
}

.about-content .nav-tabs .nav-item {
  margin-bottom: 0;
  display: inline-block;
  margin-top: 0;
}

.about-content .nav-tabs .nav-item.show .nav-link,
.about-content .nav-tabs .nav-link.active {
  color: #495057;
  background-color: #101010;
  border: none;
  border-radius: 10px;
  padding: 12px 32px;
  color: #fff;
  text-transform: uppercase;
}

.about-content .nav-tabs .nav-link {
  padding: 12px 32px;
  text-transform: uppercase;
  border: none;
}

.ab-ul {
  margin-top: 30px;
}

.ab-ul li {
  float: left;
  width: 50%;
}

.ab-ul li .icon i {
  width: 40px;
  height: 40px;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 2px solid #eeeeee;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  margin-inline-end: 20px;
  color: var(--primary-color);
}

.sr-ul {
  margin-top: 30px;
}

.sr-ul li {
  float: left;
  width: 46.2%;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.06);
  margin: 0 20px 30px 0;
  padding: 15px 20px;
}

.sr-ul li .icon i {
  width: inherit;
  height: inherit;
  background: inherit;
  border: inherit;
  border-radius: inherit;
  line-height: inherit;
  font-size: 30px;
  color: #4cc3c1;
}

.sr-ul li .text {
  font-size: 14px;
  color: var(--primary-color);
}

.sr-tw-ul {
  position: relative;
  z-index: 1;
}

.sr-tw-ul li {
  float: left;
  width: 100%;
  border: 1px solid;
  padding: 30px;
  border-width: 1px;
  border-color: rgb(215, 215, 215);
  border-style: solid;
  border-radius: 4px;
  margin-bottom: 15px;
}

.sr-tw-ul li p {
  margin-bottom: 0;
}

.sr-tw-ul li .icon {
  width: 108px;
}

.sr-tw-ul li .icon img,
.sr-tw-ul li .icon-right img {
  width: 54px;
}

.sr-tw-ul li .icon-right {
  width: 108px;
}

.sd-img {
  margin: 0 -50px;
}

.sd-img img {
  width: 100%;
}

.section-t h2 {
  font-size: 350px;
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  text-align: center;
  z-index: -1;
  color: #101010;
  opacity: 0.05;
  margin: 0;
  font-family: "Playfair Display", serif;
  font-weight: 500;
  font-style: italic;
  top: -35px;
  line-height: 1;
}

.second-about {
  transform: translate(0, 1px);
  left: 0;
  inset-inline-end: 25px;
  bottom: inherit;
  text-align: center;
  top: 0;
}

.second-about p {
  margin-bottom: 0;
  color: #fff;
  font-size: 25px;
  font-weight: 800;
  font-family: var(--primary-font), sans-serif;
  margin-top: 10px;
  line-height: 30px;
}

.second-atitle > span {
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  color: var(--primary-color);
  letter-spacing: 2px;
  display: inline-block;
  margin-bottom: 20px;
  background: transparent;
}

.s-about-content p {
  margin-bottom: 15px;
}

.about-content strong {
  color: var(--primary-color);
}

.sinature-box {
  float: left;
  width: 100%;
  box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.0392156863);
  padding: 25px 30px 15px 30px;
  margin-top: 50px;
}

.sinature-box h2 {
  font-size: 20px;
  font-weight: 500;
  font-family: var(--primary-font), sans-serif;
  color: #101010;
  margin-bottom: 5px;
}

.sinature-box h3 {
  font-size: 14px;
  font-family: var(--primary-font), sans-serif;
  color: #4cc3c1;
}

.sinature-box .user-box {
  display: flex;
}

.sinature-box .user-box .text {
  padding-top: 10px;
  padding-inline-start: 15px;
}

.signature-text {
  font-size: 22px;
  color: var(--primary-color);
  font-family: "Playfair Display", serif;
  font-weight: 600;
}

.circle-right {
  display: inline-block;
}

.ab-coutner li {
  display: inline-block;
  margin-inline-end: 22px;
}

.ab-coutner .single-counter {
  background-color: rgb(250, 162, 146);
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  padding: 70px 0;
}

.ab-coutner .single-counter p {
  margin-top: 15px;
  color: #4f4f4f;
  font-family: "Playfair Display", serif;
  font-size: 20px;
  font-weight: 600;
}

.clinet-abimg {
  position: absolute;
  bottom: -76px;
  inset-inline-end: 0;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.about-content2 p {
  color: rgba(255, 255, 255, 0.6705882353);
}

.about-content2 .about-title h2 {
  color: #fff;
}

.about-content2 li {
  display: flex;
  margin-bottom: 10px;
  color: #fff;
}

.about-content2 li .icon {
  width: 15px;
  height: 15px;
  background: var(--primary-color);
  text-align: center;
  margin-inline-end: 20px;
  color: #e11d07;
  display: block;
  margin-top: 10px;
}

.about-content2 li .text {
  padding-top: 5px;
}

.experience-text {
  float: left;
  padding: 15px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.3098039216);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3098039216);
  width: 100%;
  display: flex;
}

.experience-text span {
  font-size: 60px;
  color: var(--primary-color);
  position: relative;
  font-weight: 800;
  font-family: var(--primary-font), sans-serif;
  margin-top: 15px;
  float: left;
  margin-inline-end: 15px;
}

.experience-text span sub {
  font-size: 30px;
  color: var(--primary-color);
  font-weight: 800;
  margin-inline-start: -10px;
  bottom: 5px;
}

.exp-no {
  width: 66%;
  border-inline-end: 1px solid rgba(255, 255, 255, 0.3098039216);
}

.exp-no p {
  float: left;
  color: #fff;
  width: 48%;
}

.exp-text {
  padding-inline-start: 30px;
  color: rgba(255, 255, 255, 0.6705882353);
}

.achivments-outer {
  display: flex;
}

.achivments-outer .text {
  padding-inline-start: 15px;
}

.achivments-outer .text h5 {
  margin-bottom: 0;
  margin-top: 10px;
  font-size: 24px;
  font-weight: 600;
}

.achivments-outer .text p {
  color: var(--primary-color);
}

/* 6. services */
.feature-area {
  margin-top: -80px;
  z-index: 1;
  position: relative;
}

.services-two .s-single-services {
  border-radius: 0 0 0 0;
}

.services-two .s-single-services .btn2 {
  display: inline-block;
  color: var(--primary-color);
}

.services-area.gray-bg {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.single-services {
  background: #fff;
  box-shadow: 0px 5px 17px 0px rgba(0, 0, 0, 0.07);
  position: relative;
}

.ser-m {
  margin-inline-start: 0;
  margin-inline-end: 0;
  margin-bottom: 30px;
}

.single-services .day-book {
  background: var(--primary-color);
  font-family: var(--primary-font), sans-serif;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  margin-bottom: 30px;
  margin-top: -70px;
  position: relative;
}

.single-services .day-book a {
  color: #fff;
}

.single-services .day-book li {
  width: 100%;
  text-align: center;
}

.single-services .icon {
  margin-top: 30px;
}

.single-services .icon li {
  display: inline-block;
  margin-inline-end: 34px;
}

.single-services .icon li:last-child {
  margin-inline-end: 0;
}

.single-services .icon li img {
  height: 20px;
}

.single-services::before {
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  inset-inline-start: 5px;
  top: 5px;
  background: var(--primary-color);
  z-index: -1;
  transition: 0.3s;
  opacity: 0;
}

.service-t h2 {
  top: 55px;
}

.services-thumb img {
  width: 100%;
}

.services-content span {
  display: block;
  height: 2px;
  width: 150px;
  background: var(--primary-color);
  transition: 0.3s;
  margin-bottom: 55px;
}

.services-content {
  padding: 30px 40px 60px;
  padding-bottom: 55px;
}

.services-content small {
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: block;
  margin-bottom: 20px;
}

.services-content h4 {
  font-size: 24px;
  margin-bottom: 20px;
}

.services-content h4:hover a {
  color: var(--primary-color);
}

.services-content p {
  margin-bottom: 0;
}

.single-services:hover .services-content span {
  background: var(--primary-color);
}

.single-services:hover::before {
  opacity: 1;
}

.services-active .slick-dots {
  text-align: center;
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: -40px;
  line-height: 1;
}

.services-active .slick-dots li {
  display: inline-block;
  margin: 0 5px;
}

.services-active .slick-dots li button {
  text-indent: -99999px;
  border: none;
  padding: 0;
  height: 5px;
  width: 20px;
  background: var(--primary-color);
  border-radius: 50px;
  z-index: 1;
  cursor: pointer;
  transition: 0.3s;
}

.services-active .slick-dots li.slick-active button {
  width: 50px;
  background: var(--primary-color);
}

.services-active .slick-track {
  padding-bottom: 10px;
}

.services-icon i {
  font-size: 48px;
  border-radius: 50%;
  color: #ff4328;
  text-align: center;
  width: 100px;
  height: 100px;
  line-height: 100px;
  border: 1px solid #ff4328;
}

.services-two .services-icon {
  display: inline-block;
  width: 90px;
  height: 90px;
  background: #ffffff 0% 0% no-repeat padding-box;
  text-align: center;
  border-radius: 50%;
  padding: 25px;
  margin-bottom: 30px;
}

.services-two .services-icon img {
  width: 100%;
}

.services-two .brline {
  margin-top: 15px;
  margin-bottom: 25px;
}

.services-two ul li {
  margin-bottom: 25px;
}

.services-two ul li::before {
  font-family: "Font Awesome 5 Pro";
  content: "ï€Œ";
  margin-inline-end: 10px;
  color: var(--primary-color);
}

.services-icon .glyph-icon::before {
  font-size: 48px;
  color: #3763eb;
  margin-inline-start: 0;
}

.services-icon2 {
  position: absolute;
  inset-inline-end: 5%;
  top: 0;
  z-index: 1;
  opacity: 0.05;
}

.services-icon2 .glyph-icon::before {
  font-size: 120px;
  color: rgba(140, 168, 255, 0.1607843137);
  margin-inline-start: 0;
}

.services-two .second-services-content {
  float: left;
  width: 100%;
}

.services-two .s-single-services {
  transition: 0.3s;
  padding: 45px;
  border: none;
  margin-bottom: 30px;
  border-radius: 0;
}

.services-two .s-single-services {
  float: left;
  width: 100%;
}

.services-three .services-icon {
  display: inline-block;
  width: auto !important;
  height: auto !important;
  margin-bottom: 30px;
}

.services-three .services-icon img {
  width: 100%;
}

.services-three .brline {
  margin-top: 15px;
  margin-bottom: 25px;
}

.services-three ul li {
  margin-bottom: 25px;
}

.services-three ul li::before {
  font-family: "Font Awesome 5 Pro";
  content: "ï€Œ";
  margin-inline-end: 10px;
  color: var(--primary-color);
}

.services-three .second-services-content {
  float: left;
  width: 100%;
}

.services-three .s-single-services {
  transition: 0.3s;
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 4px;
  border: 2px solid #f0f0f0;
  display: block;
  box-shadow: none;
}

.services-three .s-single-services.active {
  background: #fff;
  margin-top: 0;
  position: relative;
  z-index: 1;
  left: 0;
}

.services-three .s-single-services.active .services-icon {
  background: #fff 0% 0% no-repeat padding-box;
  width: 90px;
  height: 90px;
}

.services-three .s-single-services.active a.ss-btn {
  display: none;
}

.second-services-content h5 {
  transition: 0.3s;
  letter-spacing: 0;
  font-size: 26px;
  color: #030c26;
}

.second-services-content p {
  margin-bottom: 0;
}

.second-services-content a {
  /*font-size: 14px;
  font-weight: 500;
  color: #444d69;
  position: relative;
  transition: .5s;*/
}

.second-services-content a::after {
  left: -40px;
  inset-inline-end: unset;
  opacity: 0;
}

.s-single-services {
  transition: 0.3s;
  background: #fff;
  position: relative;
  display: flex;
}

.s-single-services .services-icon {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
}

.h-service {
  position: absolute;
  left: 0;
  text-align: center;
  width: 100%;
  height: 100%;
  background: rgba(16, 16, 16, 0.6117647059);
  padding-top: 45%;
  transition: 0.3s cubic-bezier(0.24, 0.74, 0.58, 1);
  transform: scaleY(1);
}

.h-service h5 {
  color: #fff;
  font-size: 26px;
  font-weight: 600;
}

.s-single-services:hover .h-service {
  transform: scaleY(0);
  opacity: 0;
}

.services-hover .box1 {
  content: "";
  width: 60px;
  height: 60px;
  border: 1px solid var(--primary-color);
  position: absolute;
  z-index: 11;
  left: 40px;
  top: 40px;
  border-inline-end: 0;
  border-bottom: 0;
}

.services-hover .box2 {
  content: "";
  width: 60px;
  height: 60px;
  border: 1px solid var(--primary-color);
  position: absolute;
  z-index: 11;
  inset-inline-end: 40px;
  top: 40px;
  border-inline-start: 0;
  border-bottom: 0;
}

.services-hover {
  position: absolute;
  top: 0;
  left: 0;
  padding: 100px 60px 0;
  background: #101010;
  height: 100%;
  width: 100%;
  color: #fff;
  transform: scaleY(0);
  opacity: 0;
  transition: 0.3s cubic-bezier(0.24, 0.74, 0.58, 1);
}

.s-single-services:hover .services-hover {
  transform: scaleY(1);
  opacity: 1;
}

.services-hover h5 {
  color: #fff;
  font-weight: 600;
  margin-bottom: 15px;
}

.services-hover a {
  color: #fff;
  text-decoration: underline;
  text-transform: uppercase;
  margin-top: 15px;
  display: block;
}

.services-hover:hover p {
  height: auto;
  width: auto;
}

.services-hover:hover .services-icon {
  margin-top: 20px;
}

.second-services-content a {
  color: 0 f38;
  transition: all 0.3s ease 0s;
}

.s-single-services a.ss-btn {
  display: none;
}

.s-single-services .active-icon {
  display: none;
}

.s-single-services .d-active-icon {
  display: block;
}

.s-single-services:hover {
  background: #fff;
}

.s-single-services.active a.ss-btn {
  display: inline-block;
}

.s-single-services.active .active-icon {
  display: block;
}

.s-single-services.active .d-active-icon {
  display: none;
}

.services-bg {
  background-position: center;
  background-size: cover;
}

.services-active2 {
  padding-top: 50px;
  padding-bottom: 300px;
}

.services-active2 .slick-arrow {
  position: absolute;
  top: 50%;
  left: -60px;
  border: none;
  background: none;
  padding: 0;
  font-size: 24px;
  color: var(--primary-color);
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
}

.services-active2 .slick-next {
  left: inherit;
  inset-inline-end: -60px;
}

.services-active2 .slider-nav .slick-list.draggable {
  width: 1100px;
  float: right;
}

.services-item {
  background: #fff;
  margin: 22px 15px;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.1019607843);
  text-align: center;
  border-radius: 10px;
}

.services-active2 .slider-nav {
  position: absolute;
  bottom: 0px;
}

.services-active2 li {
  display: flex;
  margin-bottom: 10px;
}

.services-item h3 {
  font-size: 20px;
}

.services-item .glyph-icon:before {
  font-size: 60px;
}

.services-active2 li .icon {
  padding-inline-end: 10px;
}

.services-item.slick-slide.slick-current.slick-active {
  background: #3763eb;
}

.services-item.slick-slide.slick-current.slick-active .glyph-icon:before {
  color: #fff;
}

.services-item.slick-slide.slick-current.slick-active h3 {
  color: #fff;
}

.services-area .services-box {
  background: #f7f6fb;
  padding-top: 30px;
}

.services-box .services-content2 {
  margin-top: 25px;
  padding-bottom: 30px;
  transition: 0.3s;
  padding-inline-start: 30px;
  padding-inline-end: 30px;
}

.services-box .services-content2 p {
  margin-bottom: 0;
}

.services-content2 h5 {
  font-size: 24px;
  margin-bottom: 15px !important;
}

.services-content2 h5 a {
  color: #30313d;
}

.services-box .ss-btn2 {
  width: 60px;
  height: 60px;
  background: var(--primary-color) 0% 0% no-repeat padding-box;
  box-shadow: 0px 16px 32px var(--primary-color) 33;
  display: none;
  color: #fff;
  line-height: 60px;
  font-size: 31px;
  border-radius: 50%;
  position: absolute;
  left: 42%;
  bottom: -25px;
  transition: 0.3s;
}

.services-box .services-content2:hover .ss-btn2 {
  display: inline-block;
}

.services-02 ul {
  display: flex;
  border: 2px solid #f5f5f5;
}

.services-02 ul li {
  padding: 30px;
  border-inline-end: 2px solid #f5f5f5;
  background: #fff;
  transition: 0.2s;
}

.services-02 ul li:last-child {
  border: none;
}

.services-icon-02 {
  margin-bottom: 30px;
}

.services-content-02 h5 {
  font-size: 24px;
  margin-bottom: 20px;
}

.services-content-02 {
  font-size: 14px;
}

.services-content-02 .ss-btn2 {
  margin-top: 20px;
  display: block;
}

.services-content-02 .ss-btn2 i {
  width: 40px;
  height: 40px;
  border: 2px solid #e6e6e6;
  border-radius: 50%;
  line-height: 35px;
  text-align: center;
  color: #777;
}

.services-content-02 .ss-btn2 span {
  color: var(--primary-color);
  margin-inline-start: -10px;
  font-weight: 600;
  opacity: 0;
  transition: all 0.5s ease 0s;
}

.services-02 ul li:hover {
  border: none;
  box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.0392156863);
  transform: scale(1.08);
  transition: 0.2s;
}

.services-02 ul li:hover .ss-btn2 span {
  margin-inline-start: 5px;
  opacity: 1;
  transition: 0.2s;
}

.services-box-03 {
  background: #fff;
  padding: 50px;
}

.services-box-04 {
  border-radius: 3px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 8px 38px 0px rgba(40, 40, 40, 0.04);
  padding: 45px;
}

.services-icon-04 {
  display: flex;
  margin-bottom: 30px;
}

.services-icon-04 img {
  margin-inline-end: 15px;
}

.services-icon-04 h4 {
  padding: 15px 0 0;
}

.services-box-04 .services-content-02 li {
  font-weight: 500;
  font-size: 16px;
  margin-top: 10px;
  color: var(--primary-color);
}

.services-box-04 .services-content-02 li::before {
  font-family: "Font Awesome 5 Pro";
  content: "ï€Œ";
  margin-inline-end: 10px;
  color: var(--primary-color);
}

.services-box-04 .services-content-02 .blue li::before {
  color: #4cc3c1;
}

.services-box-04 .services-content-02 .green li::before {
  color: #2db065;
}

.services-05 h2 {
  color: #fff;
}

.services-05::before {
  content: "";
  background: var(--primary-color);
  position: absolute;
  top: 0;
  width: 100%;
  height: 529px;
}

.services-content-05 {
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 8px 38px 0px rgba(40, 40, 40, 0.04);
  padding: 30px;
  margin-bottom: 50px;
}

.services-content-05 span {
  color: var(--primary-color);
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  display: block;
}

.services-text05 p {
  font-size: 20px;
  color: #777 !important;
}

.services-text05 a {
  font-size: 20px;
  color: var(--primary-color);
  font-weight: 500;
  text-decoration: underline;
}

.services-icon-05 img {
  width: 100%;
}

.services-07 .section-title h2 {
  color: #fff;
}

.services-box07 {
  border-radius: 4px;
  background-color: rgb(255, 255, 255);
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.services-box07 .sr-contner {
  display: flex;
}

.services-box07 .sr-contner .icon {
  width: 119px;
  margin-inline-end: 20px;
}

.services-box07 .sr-contner .text h5 {
  font-size: 25px;
}

.services-box07 .sr-contner .text p {
  margin-top: 15px;
  margin-bottom: 0;
}

.services-07 .user-box {
  display: flex;
}

.services-07 .user-box .text {
  padding-top: 10px;
  padding-inline-start: 15px;
}

.services-07 .user-box .text h2 {
  font-size: 24px;
  font-weight: 500;
  font-family: var(--heading-font), sans-serif;
  color: #fff;
  margin-bottom: 5px;
}

.services-07 .user-box .text h3 {
  font-size: 14px;
  font-family: var(--primary-font), sans-serif;
  color: #9a9fa2;
}

.services-08-item {
  padding: 40px;
  border-radius: 0;
  background-color: rgb(255, 255, 255);
  box-shadow: 1px 1.732px 60px 0px rgba(0, 0, 0, 0.1);
  position: relative;
}

.services-08-item .readmore {
  width: 50px;
  height: 50px;
  background: #d7d7d7;
  font-size: 24px;
  text-align: center;
  line-height: 50px;
  color: #162542;
  display: inline-block;
  margin-top: 15px;
}

.services-08-item .readmore:hover {
  color: #fff;
  background: var(--primary-color);
}

.services-08 h2 {
  color: #fff;
}

.services-08 .bg-outer {
  background-color: #fff;
  -webkit-box-shadow: 0px 30px 40px 0px rgba(32, 85, 125, 0.06);
  box-shadow: 0px 30px 40px 0px rgba(32, 85, 125, 0.06);
}

.services-08 .col-lg-4.col-md-4 {
  position: relative;
}

.services-08 .services-08-item--wrapper {
  border-bottom: 2px solid #f5f5f5;
}

.services-08 .services-08-item--wrapper:nth-child(2) {
  border-bottom: none;
}

.services-08-item--wrapper > div {
  border-inline-end: 2px solid #f5f5f5;
}

.services-08-item--wrapper > div:nth-child(3) {
  border-inline-end: none;
}

.services-08-content h3 {
  margin-bottom: 15px;
  font-size: 26px;
}

.services-08-content,
.services-08-thumb {
  position: relative;
  z-index: 1;
}

.services-08-thumb img {
  height: 80px;
  margin-bottom: 20px;
}

.services-08-content .number {
  color: #f5f5f5;
  -webkit-text-stroke: 1px var(--primary-color);
  -webkit-text-fill-color: #fff;
  font-size: 70px;
  font-weight: bold;
  margin-bottom: 35px;
}

.services-08-item:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  z-index: 1;
}

.services-08-item:hover::before {
  background-color: #fff;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  top: -10px;
  left: -10px;
  background-color: #fff;
  -webkit-box-shadow: 0px 30px 40px 0px rgba(32, 85, 125, 0.06);
  box-shadow: 0px 30px 40px 0px rgba(32, 85, 125, 0.06);
}

.services-09::before {
  display: none;
}

.services-09 p,
.services-09 .section-title h5 {
  color: #fff;
}

.services-area2 .container {
  z-index: 1;
  position: relative;
}

.services-area2 h2 {
  color: #30313d;
}

.services-area2 .services-box2 {
  background-color: rgb(255, 255, 255);
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.07);
}

.services-area2 .services-box2 h3 {
  padding-inline-start: 30px;
  border-inline-start: 5px solid var(--primary-color);
}

.services-area2 .services-box2 .services-icon {
  border-radius: 15px;
  background-color: #fff7e7;
  width: 80px;
  height: 80px;
  line-height: 80px;
  display: inline-block;
  text-align: center;
}

.services-area2 .services-box2 .services-icon i {
  border: none;
  color: var(--primary-color);
}

.services-area2 .services-box2 p {
  margin-bottom: 0;
}

.services-box2 .services-content2 {
  padding: 30px 0 30px 0;
  position: relative;
}

.services-box2 .services-content2 h3 {
  font-weight: 700;
  font-size: 24px;
}

.services-box2 .services-content2 .icon {
  color: #777777;
  width: 50px;
  height: 50px;
  border: 2px solid rgba(119, 119, 119, 0.4901960784);
  text-align: center;
  font-size: 30px;
  line-height: 48px;
  display: inline-block;
  border-radius: 50%;
  position: absolute;
  top: 28%;
  inset-inline-end: 30px;
}

.services-box2 .services-content2 .icon:hover {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.animations-01 {
  position: absolute;
  left: 0;
  top: 240px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-02 {
  position: absolute;
  inset-inline-end: 0;
  bottom: 45%;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

.animations-03 {
  position: absolute;
  left: -50px;
  top: 240px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-04 {
  position: absolute;
  left: 235px;
  bottom: 195px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-05 {
  position: absolute;
  animation: alltuchtopdown 5s infinite;
  width: 100%;
  text-align: center;
  top: 24px;
  z-index: -1;
}

.animations-06 {
  position: absolute;
  left: 120px;
  top: 240px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-07 {
  position: absolute;
  left: 130px;
  bottom: 420px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-08 {
  position: absolute;
  inset-inline-end: 120px;
  top: 550px;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

.animations-09 {
  position: absolute;
  inset-inline-end: 120px;
  bottom: 150px;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

.animations-10 {
  position: absolute;
  left: 110px;
  top: 155px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-11 {
  position: absolute;
  left: 160px;
  bottom: 165px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-12 {
  position: absolute;
  left: 120px;
  top: 240px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-13 {
  position: absolute;
  left: 250px;
  bottom: 180px;
  animation: alltuchtopdown 3s infinite;
  -webkit-animation: alltuchtopdown 3s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 1s;
}

.animations-14 {
  position: absolute;
  inset-inline-end: 80px;
  top: 150px;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

.animations-15 {
  position: absolute;
  inset-inline-end: 120px;
  bottom: 0;
  animation: alltuchtopdown 5s infinite;
  -webkit-animation: alltuchtopdown 5s infinite;
  animation-delay: 0s;
  -webkit-animation-delay: 3s;
}

@keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-20px);
    -moz-transform: rotateX(0deg) translateY(-20px);
    -ms-transform: rotateX(0deg) translateY(-20px);
    -o-transform: rotateX(0deg) translateY(-20px);
    transform: rotateX(0deg) translateY(-20px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
@-webkit-keyframes alltuchtopdown {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(-20px);
    -moz-transform: rotateX(0deg) translateY(-20px);
    -ms-transform: rotateX(0deg) translateY(-20px);
    -o-transform: rotateX(0deg) translateY(-20px);
    transform: rotateX(0deg) translateY(-20px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0px);
    -moz-transform: rotateX(0deg) translateY(0px);
    -ms-transform: rotateX(0deg) translateY(0px);
    -o-transform: rotateX(0deg) translateY(0px);
    transform: rotateX(0deg) translateY(0px);
  }
}
.sbox div:nth-child(1) .services-box07 {
  background: #ffeec4;
}

.sbox div:nth-child(2) .services-box07 {
  background: #e5f3ff;
}

.sbox div:nth-child(3) .services-box07 {
  background: #e1ffe5;
}

.sbox div:nth-child(4) .services-box07 {
  background: #fff4f3;
}

.service-details-two {
  margin-top: -90px;
}

/* 15. blog */
.home-blog-active .product {
  margin-inline-start: 15px;
  margin-inline-end: 15px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0px 30px 20px 0px rgba(0, 0, 0, 0.05);
}

.home-blog-active .slick-dots {
  text-align: center;
  margin-top: 30px;
}

.home-blog-active .slick-dots li {
  display: inline-block;
  margin: 0 5px;
}

.home-blog-active .slick-dots li button {
  text-indent: -99999px;
  border: none;
  padding: 0;
  margin-inline-start: 10px;
  border-radius: 50px;
  z-index: 1;
  cursor: pointer;
  background: no-repeat;
  transition: 0.3s;
}

.home-blog-active .slick-dots li.slick-active button::before {
  background: var(--primary-color);
  opacity: 1;
}

.home-blog-active .slick-dots li button::before {
  content: "";
  width: 10px;
  height: 10px;
  background: #dbdbdb;
  float: left;
  position: relative;
  margin-top: -9px;
  inset-inline-start: -19px;
}

.home-blog-active .slick-arrow,
.home-blog-active2 .slick-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: -84px;
  border: none;
  background: none;
  padding: 0;
  color: #f6fbff;
  z-index: 1;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  cursor: pointer;
  width: 60px;
  height: 60px;
  text-align: center;
  color: var(--primary-color);
  background: #fff;
  font-size: 24px;
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.07);
}

.home-blog-active .slick-arrow.slick-next,
.home-blog-active2 .slick-arrow.slick-next {
  inset-inline-end: -84px;
  inset-inline-start: auto;
}

.home-blog-active .slick-arrow:hover,
.home-blog-active2 .slick-arrow:hover {
  background: var(--primary-color);
  color: #fff;
}

.blog-content2 .category {
  position: absolute;
  top: -250px;
  background: var(--primary-color);
  padding: 10px 19px;
  inset-inline-start: 20px;
  border-radius: 10px;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
  font-weight: 500;
  text-transform: uppercase;
  color: #fff;
}

.blog-thumb img {
  width: 100%;
}

.blog-thumb .b-meta {
  padding: 30px 25px 0;
  color: var(--primary-color);
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: 1;
}

.blog-thumb img {
  visibility: hidden;
}

.blog-thumb:hover img {
  visibility: visible;
}

.blog-content {
  background: #fff;
  padding: 40px 30px;
  position: relative;
  z-index: 1;
}

.single-post2 {
  display: inline-block;
}

.single-post .blog-content {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  background: none;
  padding-bottom: 30px;
}

.blog-content h4 {
  font-size: 19px;
  margin-bottom: 20px;
  line-height: 1.3;
}

.blog-content h4:hover a {
  color: var(--primary-color);
}

.blog-thumb:hover::after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #101010 0% 0% no-repeat padding-box;
  opacity: 0.8;
  display: inline-block;
}

.blog-thumb2 img {
  width: 100%;
  overflow: hidden;
}

.blog-thumb2 .b-meta {
  padding: 30px 25px 0;
  color: var(--primary-color);
  top: 0;
  inset-inline-start: 0;
  z-index: 1;
}

.blog-content2 {
  padding: 40px 30px 30px;
  float: left;
  position: relative;
  background: #fff;
  border: 1px solid #b7b7b7;
}

.blog-content2 .b-meta {
  color: #777;
  padding-bottom: 15px;
}

.blog-content2 .b-meta ul {
  padding-inline-start: 0 !important;
}

.blog-content2 h4 {
  font-size: 26px;
  margin-bottom: 15px;
}

.blog-content2 h4 a {
  text-decoration: none;
  line-height: 30px;
}

.blog-content2 .date-home,
.bsingle__content .date-home {
  position: absolute;
  top: -20px;
  background: var(--primary-color);
  color: #fff;
  padding: 5px 15px;
  inset-inline-start: 30px;
}

.adim-box {
  display: flex;
}

.adim-box .text {
  padding-top: 10px;
  padding-inline-start: 15px;
  font-size: 14px;
  color: #101010;
}

.blog-thumb:hover .b-meta h4,
.blog-thumb:hover .b-meta h4 a:hover {
  color: #fff;
}

.b-meta ul li {
  display: inline-block;
  margin-inline-end: 8px;
  padding-inline-end: 8px;
  color: #777;
}

.b-meta ul li:last-child {
  margin-inline-end: 0;
  padding-inline-end: 0;
  border: none;
}

.b-meta ul li a {
  font-size: 14px;
  text-transform: uppercase;
  color: #444d69;
}

.b-meta ul li a.corpo {
  color: var(--primary-color);
}

.b-meta ul li a:hover {
  color: var(--primary-color);
}

.single-post:hover .blog-btn {
  opacity: 1;
}

.blog-btn a,
.blog__btn a {
  display: inline-block;
  color: var(--primary-color);
  text-align: center;
  font-size: 15px;
  border-bottom: 3px solid var(--primary-color);
  text-transform: uppercase;
  font-weight: 500;
}

.blog-btn a:hover,
.blog__btn a:hover {
  color: var(--btn-text-color-hover);
  border-bottom: 3px solid var(--btn-text-color-hover);
}

.blog-btn2 a {
  display: inline-block;
  color: #c0c0c0;
  text-align: center;
  font-size: 14px;
}

.blog-btn a i {
  margin-inline-start: 5px;
}

.b-meta i {
  margin-inline-end: 5px;
  color: var(--primary-color);
}

.single-post {
  transition: 0.3s;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 3px 4px 15px rgba(0, 0, 0, 0.1490196078);
}

.blog-thumb {
  position: relative;
}

.bsingle__post-thumb img {
  width: 100%;
  height: auto;
}

.bsingle__post .video-p {
  position: relative;
}

.bsingle__post .video-p .video-i {
  height: 80px;
  width: 80px;
  display: inline-block;
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 80px;
  border-radius: 50%;
  background: #fff;
  color: var(--primary-color);
}

.blog-active .slick-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 40px;
  border: none;
  background: none;
  padding: 0;
  font-size: 30px;
  color: #fff;
  z-index: 9;
  opacity: 0.4;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  cursor: pointer;
}

.blog-active .slick-arrow.slick-next {
  inset-inline-end: 40px;
  inset-inline-start: auto;
}

.blog-active .slick-arrow:hover {
  opacity: 1;
}

.bsingle__content {
  padding: 40px 30px;
  background: #ffffff 0% 0% no-repeat padding-box;
  border-style: solid;
  border-width: 2px;
  border-color: #f5f5f5;
  position: relative;
}

.bsingle__content .admin {
  position: absolute;
  top: -35px;
  background: #fff;
  padding: 15px 25px;
  border-radius: 10px;
  inset-inline-end: 20px;
  border-radius: 16px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
  font-weight: 500;
}

.bsingle__content .admin i {
  margin-inline-end: 10px;
  font-size: 18px;
}

.blog-details-wrap .meta-info ul {
  padding-top: 25px;
  border-top: 2px solid #f5f5f5;
  padding-bottom: 25px;
}

.blog-details-wrap .meta-info ul li {
  display: inline-block;
  font-size: 14px;
  margin-inline-end: 15px;
  font-weight: 500;
  padding-inline-end: 15px;
  border-inline-end: 2px solid #f5f5f5;
}

.blog-details-wrap .meta-info ul li i {
  color: var(--primary-color);
  margin-inline-end: 5px;
}

.blog-details-wrap .meta-info ul li:last-child {
  border: none;
}

.blog-deatails-box02 .meta-info ul {
  margin-top: 15px;
  padding-inline-start: 0;
  padding-top: 40px;
}

.bsingle__content .meta-info ul li {
  display: inline-block;
  font-size: 14px;
  margin-inline-end: 15px;
  font-weight: 500;
}

.bsingle__content .meta-info ul li:last-child {
  border: none;
}

.bsingle__content .meta-info ul li i {
  color: var(--primary-color);
}

.bsingle__content .meta-info ul li a {
  color: #3763eb;
  text-transform: capitalize;
}

.bsingle__content .meta-info ul li a i {
  margin-inline-end: 5px;
}

.bsingle__content .meta-info ul li a:hover {
  color: var(--primary-color);
}

.bsingle__content .meta-info ul li i {
  margin-inline-end: 5px;
}

.bsingle__content h2 {
  font-size: 30px;
  line-height: 1.3;
  margin-bottom: 20px;
  font-weight: 500;
}

.bsingle__content h2:hover a {
  color: var(--primary-color);
}

.bsingle__content p {
  margin-bottom: 25px;
}

.bsingle__content .blog__btn .btn {
  font-size: 16px;
  text-transform: uppercase;
}

.bsingle__content .blog__btn .btn:hover {
  background: var(--primary-color);
  color: #fff;
}

/* 19. contact */
.contact-bg02 {
  background: #f7f5f1;
  padding: 100px;
  text-align: center;
}

.contact-bg02 h2 {
  font-size: 48px;
}

.contact-area {
  background-repeat: no-repeat;
  background-position: center center;
}

.contact-area .map {
  position: absolute;
  inset-inline-end: 0;
  top: 0;
}

.contact-wrapper textarea {
  border: 0;
  color: #000;
  font-size: 15px;
  height: 200px;
  width: 100%;
  text-transform: capitalize;
  transition: 0.3s;
  background: #f4f4fe;
  padding: 30px 40px;
}

.contact-wrapper textarea::-moz-placeholder {
  color: #b3bdcd;
  font-size: 14px;
}

.contact-wrapper textarea::placeholder {
  color: #8990b0;
  font-size: 14px;
}

.c-icon i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 40px;
  color: #8990b0;
  font-size: 14px;
}

.contact-message.c-icon i {
  top: 30px;
  transform: unset;
}

.contact-wrapper input {
  border: 0;
  color: #000;
  font-size: 15px;
  height: 80px;
  text-transform: capitalize;
  width: 100%;
  padding: 0 40px;
  transition: 0.3s;
  background: #f4f4fe;
}

.contact-wrapper input::-moz-placeholder {
  color: #8990b0;
  font-size: 14px;
}

.contact-wrapper input::placeholder {
  color: #8990b0;
  font-size: 14px;
}

.contact-name {
  position: relative;
}

.contact-field label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  color: #101010;
  margin-bottom: 20px;
  cursor: unset;
  font-family: var(--primary-font), sans-serif;
}

.contact-field {
  padding-inline-end: 0;
}

.contact-field input {
  width: 100%;
  border: none;
  padding-inline-start: 15px;
  transition: 0.3s;
  border-radius: 0;
  border: 1px solid #777;
  height: calc(3em + 0.75rem + 2px);
}

.contact-field.c-name::after {
  content: "\f007";
}

.contact-field::after {
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  display: inline-block;
  font-size: 14px;
  text-align: center;
  inset-inline-end: 25px;
  color: var(--primary-color);
  bottom: 22px;
  display: none;
}

.contact-field.c-email::after {
  content: "\f0e0";
}

.contact-field.c-subject::after {
  content: "\f249";
}

.contact-field.c-message::after {
  content: "\f303";
  font-weight: 600;
  top: 20px;
  bottom: unset;
}

.contact-field input::placeholder,
.contact-field textarea::placeholder {
  color: #9e9e9e;
}

.contact-field textarea {
  width: 100%;
  padding: 15px;
  transition: 0.3s;
  height: 115px;
  border: 1px solid #777;
}

.contact-bg {
  background-size: cover;
  background-position: center;
  z-index: 1;
}

.contact-img {
  position: absolute;
  bottom: 0;
  inset-inline-end: 170px;
  z-index: -1;
}

.quote-post {
  background-position: center;
  background-repeat: no-repeat;
}

.quote-post .quote-icon {
  float: left;
  margin-inline-end: 30px;
  display: block;
  margin-top: 20px;
}

.quote-post h2 {
  overflow: hidden;
  margin-bottom: 0;
}

.blog-deatails-box.single .bsingle__post {
  margin-bottom: 0;
}

.sidebar-widget .widget table {
  width: 100%;
  padding: 10px;
  background: #f5f5f5;
}

.sidebar-widget .widget th,
.footer-widget .widget th {
  background: var(--primary-color);
  padding: 10px 5px;
  text-align: center;
  color: #fff;
}

.sidebar-widget .widget td {
  padding: 5px;
  text-align: center;
}

.sidebar-widget .widget {
  padding: 40px;
  overflow: hidden;
  margin-bottom: 40px;
  border: 2px solid #f7f5f1 !important;
  background: #f7f5f1;
}

.sidebar-widget .widget li {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 5px;
  float: left;
  width: 100%;
}

.widget-title {
  margin-bottom: 40px;
}

.sidebar-widget .widgettitle,
.sidebar-widget .widget-title,
.wp-block-group__inner-container h2 {
  font-size: 22px;
  margin-bottom: 0;
  line-height: 1;
  text-align: start;
  font-weight: 700;
  margin-bottom: 30px;
}

.sidebar-widget .widget .gallery-icon a:hover {
  padding-inline-start: 0;
}

.tags {
  border-bottom: 1px solid #eaeaea;
  /*float:left;*/
  width: 100%;
  display: inline-block;
}

.wp-block-search .wp-block-search__label {
  display: none;
}

.search-form,
.wp-block-search__button-outside.wp-block-search__text-button.wp-block-search {
  position: relative;
}

.search-form label {
  width: 100%;
}

.search-form input,
.wp-block-search .wp-block-search__input {
  background: #fff;
  width: 100%;
  padding: 17px 30px;
  border: 1px solid var(--input-border-color);
}

.search-form input::placeholder {
  font-size: 12px;
  color: #b0b0b0;
}

.search-form .search-submit,
.wp-block-search .wp-block-search__button {
  position: absolute;
  inset-inline-end: 0;
  top: 0;
  border: none;
  background: var(--primary-color);
  padding: 18px 23px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  min-height: 88%;
  width: 106px;
}

.sidebar-widget .widget .widget_archive,
.widget_categories,
.sidebar-widget .widget_archive {
  text-align: right;
}

.sidebar-widget .widget .widget_archive,
.sidebar-widget .widget_categories a,
.sidebar-widget .widget_archive a {
  float: left;
  height: 30px;
}

.widget-insta-post li {
  display: inline-block;
  margin: 5px 3px;
}

.widget-insta-post li:hover a::before {
  opacity: 0.7;
}

.widget-insta-post li a {
  position: relative;
  display: block;
}

.widget-insta-post li a::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  inset-inline-start: 0;
  top: 0;
  background: var(--primary-color);
  opacity: 0;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.widget-social a {
  height: 40px;
  width: 40px !important;
  line-height: 40px !important;
  border-radius: 50%;
  display: inline-block;
  background: transparent;
  border: 1px solid var(--primary-color);
  color: #101010;
  margin: 0 3px;
  text-align: center !important;
}

.widget-social a:hover {
  background: var(--primary-color);
  color: #ffffff !important;
  border-color: var(--primary-color);
  padding: 0 !important;
  box-shadow: 2px 2px 11px 0px var(--primary-color) !important;
}

.rpwwt-widget a {
  padding-inline-start: 0 !important;
}

.rpwwt-widget li {
  line-height: 23px !important;
}

.cat__list li {
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e4e4;
  margin-bottom: 10px;
}

.cat__list li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border: none;
}

.cat__list li:hover a {
  color: var(--primary-color);
}

.cat__list li a {
  font-size: 14px;
  color: #777777;
}

.cat__list li a span {
  float: right;
  display: block;
}

.widget__post ul li {
  margin-bottom: 20px;
  overflow: hidden;
}

.widget__post ul li:last-child {
  margin-bottom: 0;
}

.widget__post-thumb {
  float: left;
  display: block;
  margin-inline-end: 20px;
}

.widget__post-content {
  overflow: hidden;
  display: block;
}

.widget__post-content h6 {
  font-size: 16px;
  margin-bottom: 1px;
  padding-inline-end: 15px;
}

.widget__post-content h6:hover a {
  color: var(--primary-color);
}

.widget__post-content span {
  font-size: 14px;
}

.widget__post-content span i {
  margin-inline-end: 8px;
}

.widget__tag ul li {
  display: inline-block;
  margin: 5px 3px;
}

.widget__tag ul li:hover a {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}

.widget__tag ul li a {
  display: block;
  border: 1px solid #d8d8d8;
  font-size: 14px;
  color: #8f8bb8;
  padding: 9px 20px;
}

.widget.widget__banner {
  border: none;
  padding: 0;
  position: relative;
}

.widget__banner-thumb img {
  width: 100%;
}

.widget__banner-overly {
  position: absolute;
  top: 50px;
  inset-inline-start: 0;
  inset-inline-end: 0;
  text-align: center;
}

.widget__banner-overly > span {
  font-size: 14px;
  text-transform: uppercase;
  color: #ffffff;
  letter-spacing: 2px;
  display: block;
  margin-bottom: 160px;
}

.widget__banner-overly h3 {
  color: #ffffff;
  font-size: 40px;
  margin-bottom: 129px;
}

.widget__banner-overly h3 span {
  display: block;
  font-family: "Roboto", sans-serif;
  font-size: 30px;
  font-weight: 300;
}

.widget__banner-overly .btn {
  border-radius: unset;
  background: #fff;
  border: 2px solid #fff;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 2px;
  padding: 17px 35px;
  color: var(--primary-color);
}

.widget__banner-overly .btn:hover {
  background: transparent;
  color: #fff;
}

.meta__info ul {
  margin-bottom: 7px;
}

.meta__info ul li {
  display: inline-block;
  font-size: 14px;
  margin-inline-end: 30px;
}

.meta__info ul li a {
  color: #777777;
  text-transform: capitalize;
}

.meta__info ul li a i {
  margin-inline-end: 5px;
}

.meta__info ul li a:hover {
  color: var(--primary-color);
}

.meta__info ul li i {
  margin-inline-end: 5px;
}

.details__content h2 {
  font-size: 32px;
  line-height: 1.3;
  margin-bottom: 20px;
  padding-inline-end: 0;
}

.details__content p {
  margin-bottom: 18px;
}

blockquote,
.pages-content blockquote,
.comment-list blockquote {
  background: #101010;
  border-radius: 10px;
  padding: 50px 45px 57px !important;
  position: relative;
  z-index: 1;
  text-align: center;
  margin-top: 30px;
  margin-bottom: 30px;
  display: inline-block;
  width: 100%;
  font-size: 18px;
}

.comment-list blockquote a:hover {
  color: #fff;
}

.news-text blockquote h3 {
  color: #fff;
}

.news-text blockquote i {
  font-size: 190px;
  color: #e4e9ff;
  z-index: -1;
  position: absolute;
  bottom: 15px;
  inset-inline-end: 30px;
}

blockquote p {
  color: #fff;
}

.news-text blockquote footer {
  color: #ae6957 !important;
  font-weight: 600;
  margin-bottom: 15px;
}

.news-text figure img {
  display: inline-block;
  margin-inline-end: 30px;
  margin-bottom: 15px;
}

.news-text ul li {
  line-height: 30px;
  list-style: none;
}

.news-text ul li i,
.quote-post ul li i {
  color: #f15b26;
  padding-inline-end: 5px;
}

.news-text blockquote p,
.pages-content blockquote p,
.comment-list blockquote p {
  color: #fff;
  line-height: 28px;
}

.news-text blockquote footer,
.pages-content blockquote footer {
  font-size: 14px;
  margin-top: 15px;
  color: #ae6957;
  font-weight: 600;
}

.wp-block-media-text .wp-block-media-text__content {
  width: 100%;
}

.wp-block-pullquote cite {
  color: #bacdff !important;
}

cite {
  margin-top: 15px;
  color: var(--primary-color);
  font-weight: 600;
}

.post-categories li {
  display: none !important;
}

.post-categories li:first-child {
  display: block !important;
}

.details__content-img {
  margin: 45px 0;
}

.details__content-img img {
  width: 100%;
  height: auto;
}

.details__content figure {
  margin-top: 45px;
  margin-bottom: 60px;
}

.details__content figure img {
  float: left;
  width: 255px;
  margin-inline-end: 30px;
}

.tags.pb-50 {
  padding-bottom: 20px;
}

.post__tag h5 {
  font-size: 22px;
  margin-bottom: 20px;
  font-weight: 600;
  padding-top: 30px;
  border-top: 2px solid #f5f5f5;
  margin-top: 25px;
}

.post__tag ul li {
  display: inline-block;
  margin-inline-end: 10px;
}

.post__tag ul li a {
  font-size: 12px;
  text-transform: uppercase;
  border: 2px solid #e5e5e5;
  padding: 7px 10px;
  display: inline-block;
  border-radius: 3px;
  color: #9d9d9d;
  margin-bottom: 10px;
}

.post__tag ul li a:hover {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: #ffffff;
}

.post__share h5 {
  font-size: 18px;
  margin-bottom: 30px;
}

.post__share ul li {
  margin-inline-start: 20px;
  display: inline-block;
}

.post__share ul li a {
  font-size: 18px;
  display: inline-block;
  color: #b5becc;
}

.post__share ul li a:hover {
  color: var(--primary-color);
}

.posts_navigation {
  border-top: 1px solid #eaeaea;
  border-bottom: 1px solid #eaeaea;
}

.posts_navigation .prev-link span {
  font-size: 12px;
  text-transform: uppercase;
  display: block;
  letter-spacing: 2px;
  margin-bottom: 15px;
}

.posts_navigation .prev-link h4 {
  font-size: 22px;
  margin-bottom: 0;
  text-transform: capitalize;
  height: 30px;
  overflow: hidden;
}

.navigation.posts-navigation {
  display: none;
}

.avatar {
  border-radius: 50% !important;
}

.wpding .footer-widget .widget_recent_comments ul li,
.wpding .footer-widget .widget_rss ul li,
.wpding .footer-widget .widget_meta ul li {
  padding-bottom: 0px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 0px;
  width: 100%;
  text-align: right;
  color: #62605c;
  min-height: 42px;
  line-height: 28px;
}

.wpding .footer-widget .widget_pages li .children li {
  min-height: 42px;
}

.comment-list .pingback,
.comment-list .trackback {
  border-top: 1px solid #d1d1d1;
  border-top-color: rgb(209, 209, 209);
  border-top-color: rgb(209, 209, 209);
  padding: 1.75em 0;
  margin-bottom: 0;
}

.avatar_post img {
  border-radius: 50%;
  box-shadow: 1px 5px 6px rgba(138, 138, 138, 0.2);
}

.posts_navigation .prev-link h4:hover a {
  color: var(--primary-color);
}

.posts_navigation .next-link span {
  font-size: 12px;
  margin-bottom: 15px;
  display: block;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.posts_navigation .next-link h4 {
  font-size: 22px;
  margin-bottom: 0;
  height: 30px;
  overflow: hidden;
}

.posts_navigation .next-link h4:hover a {
  color: var(--primary-color);
}

.related__post .post-title {
  margin-bottom: 35px;
}

.related__post .post-title h4 {
  font-size: 26px;
  margin-bottom: 0;
}

.related-post-wrap .post-thumb img {
  width: 100%;
}

.related-post-wrap .rp__content {
  padding: 30px;
  border: 2px solid #f4f3fb;
  border-top: none;
}

.related-post-wrap .rp__content h3 {
  font-size: 24px;
  margin-bottom: 20px;
  line-height: 1.4;
}

.related-post-wrap .rp__content h3:hover a {
  color: var(--primary-color);
}

.related-post-wrap .rp__content p {
  margin-bottom: 0;
  font-size: 14px;
}

.avatar__wrap {
  background: #f9f9f9;
  padding: 50px 80px;
  float: left;
  width: 100%;
}

.avatar__wrap .avatar-img {
  margin-top: -115px;
  margin-bottom: 35px;
}

.avatar__wrap-content p {
  font-size: 14px;
  margin-bottom: 0;
}

.avatar__info h5 {
  font-size: 26px;
  margin-bottom: 10px;
}

.avatar__info-social {
  margin-bottom: 20px;
}

.avatar__info-social a {
  font-size: 12px;
  color: var(--primary-color);
  display: inline-block;
  margin: 0 5px;
}

.avatar__info-social a:hover {
  color: var(--primary-color);
}

code {
  font-size: 15px;
  display: inline-block;
  font-family: var(--primary-font), sans-serif;
  margin-top: 10px;
}

figcaption {
  font-weight: 600;
  color: #202020;
}

.comment-form-comment label {
  display: none;
}

.form-submit {
  margin-bottom: 0;
}

.comment__wrap {
  border-bottom: 1px solid #eaeaea;
}

.comment__wrap-title {
  margin-bottom: 35px;
}

.comment__wrap-title h5 {
  font-size: 26px;
  margin-bottom: 0;
}

.single__comment .comments-avatar {
  float: left;
  width: 100px;
  margin-inline-end: 30px;
}

.single__comment.children {
  margin-inline-start: 130px;
}

.single__comment.children .avatar-name h6 i {
  font-size: 12px;
  color: #cacfef;
  margin-inline-start: 20px;
}

.comment-text {
  overflow: hidden;
}

.comment-text .avatar-name {
  overflow: hidden;
}

.comment-text .avatar-name h6 {
  font-size: 22px;
  margin-bottom: 7px;
}

.comment-text .avatar-name h6 i {
  display: none;
}

.comment-author-admin .avatar-name h6 i {
  font-size: 12px;
  display: inline-block;
  color: #cacfef;
  margin-inline-start: 20px;
}

.comment-text .avatar-name span {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: var(--primary-color);
}

.comment-text .comment-reply {
  display: inline-block;
  border: 2px solid #f5f5f5;
  padding: 8px 18px;
  border-radius: 5px;
  font-size: 14px;
  position: relative;
  width: 100px;
}

.comment-text .comment-reply a {
  color: #777777;
  width: 100%;
  position: absolute;
  inset-inline-start: 0;
  display: inline-block;
  top: -2px;
  padding: 10px 0 10px 41px;
}

.comment-text .comment-reply:hover a {
  color: #fff;
}

.comment-list {
  padding: 0;
  border-bottom: 1px solid #eaeaea;
  margin-top: 45px;
  margin-bottom: 45px;
  padding-bottom: 45px;
}

.comments-title {
  font-size: 22px;
  margin-bottom: 0;
}

.comment-reply-title {
  font-size: 20px;
  margin-bottom: 20px;
}

.comment-author-admin {
  margin-inline-start: 130px;
}

.comment-text .comment-reply:hover {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: #ffffff;
}

.comment-text .comment-reply i {
  margin-inline-end: 5px;
}

.comment-text p {
  font-size: 16px;
  margin-bottom: 0;
}

.screen-reader-text {
  display: none;
}

.comment-form {
  padding: 50px;
  background: #f5f5f5;
  border-radius: 5px;
}

.comment-form .comment-field {
  position: relative;
}

.comment-form .comment-field.text-area i {
  top: 25px;
  transform: unset;
}

.comment-form i {
  position: absolute;
  inset-inline-end: 30px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 14px;
}

.comment-form textarea {
  height: 150px;
  width: 100%;
  padding: 20px;
  padding-inline-end: 50px;
  background: #ffffff;
  border: none;
}

.comment-form textarea::placeholder {
  font-size: 14px;
  color: #a7a7c1;
}

.comment-form textarea:focus {
  outline: 1px solid var(--primary-color);
}

.comment-form input {
  width: 100%;
  padding: 20px;
  padding-inline-end: 50px;
  background: #ffffff;
  border: none;
}

.comment-form input::placeholder {
  font-size: 14px;
  color: #b0b0b0;
}

.comment-form input:focus {
  outline: 1px solid var(--primary-color);
}

.comment-form .submit {
  background: var(--primary-color);
  border: none;
  font-size: 14px;
  text-transform: uppercase;
  color: #fff;
  border-radius: 0px;
  padding: 12px 45px;
  margin-top: 20px;
  border-radius: 5px;
  width: auto;
}

.comment-form .submit::before {
  content: none;
}

.comment-form .submit:hover {
  color: #fff;
  background: var(--primary-color);
}

.page .comments-area ol .comment-respond {
  margin-bottom: 30px;
  margin-top: -26px;
}

.comments-area ol > .comment-respond {
  margin-bottom: 30px;
  margin-top: -30px;
}

.comments-area ol > li > ol .comment-respond {
  margin-inline-start: -32px;
  margin-top: -35px;
}

.comments-area ol > li > ol > li > ol > .comment-respond {
  margin-inline-start: -42px;
  margin-top: -30px;
}

.comments-area ol > li > ol > li > ol > li > ol > .comment-respond {
  margin-inline-start: -62px;
  margin-top: -35px;
}

.comment-reply-title small {
  margin-inline-start: 15px;
}

.blist li {
  list-style: none !important;
}

.wp-block-gallery.columns-3.is-cropped {
  margin-top: 30px;
}

figure.aligncenter img {
  margin-bottom: 15px;
}

.wp-image-907 {
  width: 100% !important;
  height: auto;
  margin-bottom: 15px;
}

figure img {
  display: inline-block;
  margin-bottom: 15px;
}

.tag-markup-2 ol li:last-child,
.tag-markup-2 ul li:last-child {
  margin-bottom: 20px;
}

.bsingle__content table th,
.pages-content table th,
.comment-text table th {
  color: #101010;
}

.bsingle__content table a,
.pages-content table a,
.comment-text table a {
  color: #76bc02;
  outline: medium none;
}

.footer-widget select {
  width: 100%;
  padding: 3px 0;
  border: 2px solid #e4e4e4;
}

.footer-widget .tag-cloud-link {
  color: #020202;
}

.footer-widget .textwidget,
.footer-widget .recentcomments a,
.footer-widget a,
.footer-widget .widget_categories a {
  color: #99a3ac;
}

.footer-widget .tag-cloud-link:hover {
  color: #fff;
}

.single-post .bsingle__content .admin {
  display: none;
}

.single-post .blog-deatails-box.single p {
  margin-bottom: 25px;
}

.single-post .blog-deatails-box.single {
  padding: 40px;
  border-style: solid;
  border-width: 2px;
  border-color: rgb(243, 243, 243);
  float: left;
  width: 100%;
}

.single-post .blog-deatails-box.single p.form-submit {
  margin-bottom: 0;
}

.blog-deatails-box.single .bsingle__content {
  padding: 0;
  border: none;
}

.single-post .bsingle__post-thumb img {
  margin-bottom: 50px;
}

blockquote h3 {
  color: #fff;
}

blockquote footer {
  color: #ae6957;
  margin-bottom: 30px;
}

.blist li i {
  color: #f15b26;
  padding-inline-end: 5px;
}

.blist li {
  line-height: 30px;
  list-style: none;
}

.comment-form-cookies-consent input {
  width: auto;
  margin-top: 7px;
  margin-inline-end: 10px;
}

.comment-text ul,
.comment-text ol {
  margin-bottom: 15px;
  padding-inline-start: 20px;
}

.bsingle__content .meta-info ul {
  padding-bottom: 0;
  border-bottom: none;
  display: inline-block;
  width: 100%;
  padding-inline-start: 0;
  margin-bottom: 20px;
}

.blog-deatails-box02 .meta-info ul {
  margin-top: 0;
  padding-inline-start: 0;
  padding-top: 0px;
}

.single-post .blog-deatails-box.single p {
  margin-bottom: 25px;
}

.sticky .bsingle__content .meta-info ul {
  padding-top: 0;
  border-top: none;
  display: inline-block;
  width: 100%;
  padding-inline-start: 0;
}

.single-post .blog-deatails-box.single p.form-submit {
  margin-bottom: 0;
}

.blog-deatails-box.single .bsingle__content {
  padding: 0;
  border: none;
}

.sidebar-widget .widget li {
  padding-bottom: 5px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 5px;
  float: left;
  width: 100%;
  min-height: 42px;
  color: #101010;
}

.sidebar-widget .widget a {
  width: 100%;
  text-align: start;
  line-height: 28px;
  margin-bottom: -26px;
  position: relative;
  z-index: 1;
}

.wp-block-cover-text {
  color: #fff !important;
  padding: 30px;
}

.inner-linke-page a,
.post-page-numbers {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.inner-linke-page a:hover,
.post-page-numbers:hover {
  color: #ffffff;
  background: #010f2e;
}

.inner-linke-page > span,
.post-page-numbers.current {
  border: none;
  height: 32px;
  width: 32px;
  display: inline-block;
  line-height: 32px;
  font-weight: 400;
  border-radius: 50%;
  font-size: 14px;
  text-align: center;
  background: #010f2e;
  color: #ffffff;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.32);
}

.wp-block-media-text__media img {
  width: 100%;
  height: auto;
}

.wp-block-button__link {
  border: none;
  font-weight: 700;
  padding: 0.76rem 1rem;
  outline: none;
  outline: none;
  display: inline-block;
  background: #101010;
  color: #fff !important;
  width: auto;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
}

.tag-markup-2 ol li:last-child,
.tag-markup-2 ul li:last-child {
  margin-bottom: 20px;
}

.bsingle__content table th,
.pages-content table th,
.comment-text table th {
  color: #101010;
}

.bsingle__content table a,
.pages-content table a,
.comment-text table a {
  color: var(--primary-color);
  outline: medium none;
}

.sidebar-widget .widget_nav_menu .sub-menu {
  display: block;
  position: relative;
  margin-top: 5px !important;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 1px solid #ddd;
}

.with-avatar .avatar {
  border-radius: 50% !important;
  display: none !important;
}

.ab-submenu .ab-item img {
  display: none !important;
}

.avatar {
  border-radius: 50% !important;
}

.wpding .footer-widget .widget_recent_comments ul li,
.wpding .footer-widget .widget_rss ul li,
.wpding .footer-widget .widget_meta ul li {
  padding-bottom: 0px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 0px;
  width: 100%;
  text-align: right;
  color: #62605c;
  min-height: 42px;
  line-height: 28px;
}

.comment-list .pingback,
.comment-list .trackback {
  border-top: 1px solid #d1d1d1;
  border-top-color: rgb(209, 209, 209);
  border-top-color: rgb(209, 209, 209);
  padding: 1.75em 0;
  margin-bottom: 0;
}

.wpding .footer-widget .widget ul li,
.wpding .footer-widget .widget ul li a,
.wpding .footer-widget p {
  color: #62605c;
}

.wpding .footer-widget .widget ul li:hover,
.wpding .footer-widget .widget ul li a:hover {
  color: var(--primary-color);
}

.wpding .footer-widget .widget_recent_comments ul li,
.wpding .footer-widget .widget_rss ul li,
.wpding .footer-widget .widget_meta ul li {
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e4e4;
  padding-top: 0px;
  width: 100%;
  text-align: start;
  color: #62605c;
  margin-bottom: 10px;
}

.wpding .footer-widget .widgettitle {
  text-align: start;
  color: #0d0d0d;
  width: 100%;
  display: inline-block;
}

.sidebar-widget .widget .children,
.footer-bg .widget .children {
  padding-inline-start: 15px;
}

.sidebar-widget .widget_nav_menu .sub-menu {
  display: block;
  position: relative;
  margin-top: 5px !important;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 1px solid #ddd;
}

.footer-top.wpding .menu .sub-menu {
  display: block;
  position: relative;
  margin-top: 0;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 1px solid #ddd;
  box-shadow: none;
  padding-inline-start: 10px;
  float: left;
  line-height: 28px;
  min-width: 100%;
}

.footer-top.wpding .menu .sub-menu li {
  float: none;
  line-height: 40px;
}

.footer-widget .menu-item-has-children {
  border-bottom: 1px solid #ddd;
  line-height: 40px;
}

.footer-bg .wpding .widget {
  margin-bottom: 30px;
}

.blog-deatails-box.single .single {
  display: none;
}

.footer-widget .tag-cloud-link,
.footer-widget p {
  color: rgba(255, 255, 255, 0.8);
}

.wpding .footer-widget .tag-cloud-link {
  color: #62605c;
}

.calendar_wrap {
  color: #777;
}

.logo {
  width: 146px;
}

.logo a {
  color: #fff;
}

blockquote a,
blockquote cite {
  color: #bacdff;
}

.booking-area .contact-form {
  background: #fff;
  padding: 50px 50px 30px 50px;
  box-shadow: 0px 20px 60px 0px rgba(21, 21, 21, 0.2);
  position: relative;
  z-index: 1;
  margin-top: -100px;
}

.booking-area2 {
  margin-top: 100px;
}

.booking-area ul li {
  margin-inline-end: 10px;
  padding-inline-end: 10px;
  float: left;
  width: 15.5%;
}

.booking-area ul li:last-child {
  margin-inline-end: 0;
}

.booking-area input {
  width: 100%;
  border: none;
  background: #fff;
  padding: 10px 20px;
  transition: 0.3s;
  border: 1px solid var(--input-border-color);
}

.booking-area select {
  display: inline-block;
  width: 100%;
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: calc(3em + 0.55rem + 2px);
  border: 1px solid var(--input-border-color);
}

.booking-area .btn {
  border-radius: 0;
  color: #fff;
  position: relative;
  border: none;
  text-align: center;
  text-transform: uppercase;
  font-weight: 500;
}

.booking-area .slider-btn label {
  color: var(--secondary-color);
  font-size: 30px;
  margin-bottom: 18px;
  width: 100%;
  text-align: center;
}

.booking-area .contact-field i {
  padding-inline-end: 5px;
}

.booking-area h2 {
  color: #101010;
  font-size: 40px;
}

.booking-area h5 {
  color: #fff;
  text-transform: uppercase;
}

.booking-area p {
  color: #fff;
}

.booking-content-box a {
  color: #fff;
  border-bottom: 5px solid #fff;
  margin-top: 15px;
  display: inline-block;
  padding-bottom: 5px;
}

.booking-contact-box {
  background: #fff;
  padding: 60px;
}

.booking-contact-box .nav.nav-tabs li {
  width: 50%;
  text-align: center;
  font-weight: 700;
  font-size: 20px;
}

.booking-contact-box .nav.nav-tabs li a {
  color: #777777;
  font-size: 20px;
  border: none;
  text-align: center;
  font-weight: 700;
  padding: 14px;
  display: inline-block;
  width: 100%;
}

.booking-contact-box .nav.nav-tabs li.active a,
.booking-contact-box .nav.nav-tabs li a.active {
  border: none;
  border-bottom-color: currentcolor;
  border-bottom-style: none;
  border-bottom-width: medium;
  border-bottom: 2px solid var(--primary-color);
  color: #000;
}

.wpding .footer-widget .widget_categories ul li a,
.wpding .footer-widget .widget_archive ul li a {
  width: 100%;
  text-align: start;
  line-height: 28px;
  margin-bottom: -26px;
  position: relative;
  z-index: 2;
  float: left;
  color: #62605c;
}

.wpding .footer-widget li:hover {
  color: #faa292;
}

.wpding .footer-widget ul li:last-child,
.wpding .footer-widget .children li:last-child {
  border-bottom: none;
}

.wpding .footer-widget .widget_categories .children li:last-child,
.wpding .footer-widget .widget_archive .children li:last-child {
  padding-bottom: 0;
  margin-bottom: -8px;
}

.wpding .footer-widget .widget_categories ul li:last-child,
.wpding .footer-widget .widget_archive ul li:last-child {
  margin-bottom: 30px;
}

.wpding .footer-widget .widget_pages ul li {
  padding-bottom: 0px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  padding-top: 0px;
  width: 100%;
  text-align: right;
  color: #62605c;
  min-height: 42px;
}

.wpding .footer-widget .widget_pages li .children {
  border-top: 1px solid rgba(255, 255, 255, 0.3019607843);
  margin-top: 0;
  padding-bottom: 0px;
  display: inline-block;
  width: 100%;
}

.wpding .footer-widget .widget_pages ul li a {
  width: 100%;
  text-align: start;
  margin-bottom: 0;
  position: relative;
  z-index: 2;
  display: inline-block;
  color: #62605c;
  line-height: 40px;
  min-height: 40px;
}

.wpding .footer-widget .tag-cloud-link {
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.3019607843);
}

.wpding .footer-widget .widget ul li,
.wpding .footer-widget .widget ul li a,
.wpding .footer-widget p {
  color: #fff;
}

.wpding .footer-widget .widget ul li:hover,
.wpding .footer-widget .widget ul li a:hover {
  color: #bacdff;
}

.wpding .footer-widget .widgettitle {
  text-align: start;
  color: #fff;
  width: 100%;
  display: inline-block;
}

.wpding .footer-widget .widget_recent_comments ul li,
.wpding .footer-widget .widget_rss ul li,
.wpding .footer-widget .widget_meta ul li {
  padding-bottom: 0px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  padding-top: 0px;
  width: 100%;
  text-align: right;
  color: #fff;
  min-height: 42px;
  line-height: 28px;
}

.comment-list .pingback,
.comment-list .trackback {
  border-top: 1px solid #d1d1d1;
  border-top-color: rgb(209, 209, 209);
  border-top-color: rgb(209, 209, 209);
  padding: 1.75em 0;
  margin-bottom: 0;
}

.wpding .footer-widget .widget_recent_comments ul li,
.wpding .footer-widget .widget_rss ul li,
.wpding .footer-widget .widget_meta ul li {
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  padding-top: 0px;
  width: 100%;
  text-align: start;
  color: #fff;
  margin-bottom: 10px;
}

.footer-bg .wpding .footer-widget .widget_archive:nth-child(1) {
  margin-top: 100px;
}

.footer-bg .wpding .widget_nav_menu {
  margin-bottom: 100px !important;
}

.footer-top.wpding .menu .sub-menu {
  display: block;
  position: relative;
  margin-top: 0;
  border: none;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top-color: currentcolor;
  border-top-style: none;
  border-top-width: medium;
  border-top: 1px solid rgba(255, 255, 255, 0.3019607843);
  box-shadow: none;
  padding-inline-start: 10px;
  float: left;
  line-height: 28px;
  min-width: 100%;
  background: none;
}

.footer-top.wpding .menu .sub-menu li {
  float: none;
  line-height: 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  display: inherit;
}

.footer-top.wpding .menu .sub-menu li:last-child {
  border: none;
}

.footer-widget .menu-item-has-children {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  line-height: 40px;
  display: inline-block;
  width: 100%;
}

.footer-bg .wpding .widget {
  margin-bottom: 30px;
}

.wpding .footer-widget .widget_categories ul li,
.wpding .footer-widget .widget_archive ul li {
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3019607843);
  padding-top: 5px;
  float: left;
  width: 100%;
  text-align: right;
  color: #fff;
}

.wpding .footer-widget .widget li .children {
  border-top: 1px solid rgba(255, 255, 255, 0.3019607843);
  margin-top: 10px;
  padding-bottom: 0px;
  display: inline-block;
  width: 100%;
}

.sidebar-widget .widget li:last-child,
.sidebar-widget .widget .children li:last-child,
.sidebar-widget .widget .children .children li:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: -8px;
}

.sidebar-widget .widget_nav_menu .sub-menu li:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.footer-top.wpding {
  background: #010c26;
}

/* booking */
.booking {
  z-index: 1;
  position: relative;
}

.bom {
  margin-top: 0;
}

.booking .contact-bg02 {
  background: none;
  padding: 0 0 0 30px;
  text-align: start;
}

.booking-img img {
  width: 100%;
  inset-inline-end: -15px;
  position: relative;
}

.booking .contact-field select {
  padding: 15px 15px;
}

.booking .contact-field input,
.booking .contact-field select {
  width: 100%;
  border: none;
  background: #fff;
  padding: 10px 20px;
  transition: 0.3s;
  border: 1px solid var(--input-border-color);
}

.booking .contact-field select,
.booking .contact-field input[type=date] {
  color: #000;
}

.booking input::placeholder,
.booking input[type=date]::placeholder,
.booking select::placeholder {
  color: #000;
}

.booking-img2 {
  position: absolute;
  bottom: -70px;
  inset-inline-start: -90px;
}

.booking .contact-bg02 label i {
  margin-inline-end: 5px;
  color: #101010;
}

/* 24. Login */
.zcube-login {
  padding: 90px;
  border: 2px solid #eaedff;
}

@media only screen and (min-width: 992px) and (max-width: 1200px) {
  .zcube-login {
    padding: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .zcube-login {
    padding: 40px;
  }
}
@media (max-width: 767px) {
  .zcube-login {
    padding: 30px;
  }
}
.zcube-login h3 {
  font-size: 30px;
}

.zcube-login input {
  width: 100%;
  height: 60px;
  border: 2px solid #eaedff;
  color: #6f7172;
  padding: 0 20px;
  margin-bottom: 20px;
}

.zcube-login label {
  color: #222;
  display: block;
}

.zcube-login label span {
  color: #fe4536;
}

.login-action input {
  width: inherit;
  height: auto;
}

.login-action label {
  display: inline-block;
  margin-inline-start: 5px;
}

.or-divide {
  border-top: 2px solid #eaedff;
  margin: 40px 0;
  text-align: center;
  position: relative;
}

.or-divide span {
  position: absolute;
  left: 0;
  inset-inline-end: 0;
  top: -9px;
  background: #ffffff;
  display: inline-block;
  width: 40px;
  margin: auto;
  line-height: 1;
  font-weight: 500;
}

@media (max-width: 767px) {
  .forgot-login {
    float: none;
  }
}
.forgot-login a {
  color: #fe4536;
}

.forgot-login a:hover {
  color: #84b77c;
}

@media (max-width: 767px) {
  .log-rem {
    float: none;
    margin-bottom: 10px;
    display: block;
  }
}
/*Services Category*/
.services-sidebar .sidebar-widget {
  background: #f3f4f8;
  padding: 40px;
  margin-bottom: 30px;
}

.services-categories {
  position: relative;
}

.services-categories li {
  position: relative;
  border: 1px solid #e7e7e7;
  margin-bottom: 15px;
  z-index: 1;
}

.services-categories li:last-child {
  border-bottom: 1px solid #e7e7e7;
}

.services-categories li:before {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 4px;
  background-color: #30313d;
  content: "";
  opacity: 0;
  visibility: hidden;
}

.services-categories li.active:before,
.services-categories li:hover:before {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.services-categories li a {
  font-size: 16px;
  font-weight: 700;
  color: #222222;
  line-height: 30px;
  padding: 10px 25px;
  display: block;
  overflow: hidden;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  background: #fff;
}

.services-categories li a:hover {
  background: var(--primary-color);
}

.services-categories li.active a,
.services-categories li:hover a {
  color: #ffffff;
  background: var(--primary-color);
}

.services-categories li a:before {
  position: absolute;
  inset-inline-end: 0;
  top: 0;
  font-size: 12px;
  line-height: 30px;
  color: #222222;
  font-weight: 700;
  content: "\f061";
  font-family: "Font Awesome 5 pro";
  transition: all 300ms ease;
  width: 50px;
  height: 50px;
  background: #e8eaec;
  text-align: center;
  line-height: 50px;
}

.services-categories li.active a:before,
.services-categories li:hover a:before {
  color: #ffffff;
}

.services-categories li:hover a::before,
.services-categories li.active a::before {
  background: var(--primary-color);
}

/*=== Services Contact Box ===*/
.service-detail figure img {
  width: 100%;
}

.service-detail .price h2 {
  margin-bottom: 10px !important;
}

.service-detail .room-features {
  margin: 0;
  margin-bottom: 0px;
  padding: 0;
  padding-top: 0px;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding-top: 13px;
}

.service-detail .room-features li {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 160px;
  font-weight: 400;
  font-size: 18px;
  color: #101010;
  font-family: var(--primary-font), sans-serif;
}

.service-detail .room-features li i {
  margin-inline-end: 10px;
  color: var(--secondary-color);
}

.service-detail .price span {
  color: var(--primary-color);
  font-weight: 600;
}

.service-detail-contact h3 {
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 30px;
  color: #fff;
}

.service-detail-contact {
  background-color: var(--primary-color);
  padding: 40px;
  text-align: center;
  margin-top: 50px;
  margin-bottom: 50px;
}

.service-detail-contact h3::before {
  content: "";
  position: absolute;
  width: 30px;
  height: 6px;
  background-color: #fff;
  border-radius: 10px;
  left: 50%;
  bottom: 0;
  transform: translate(-50%);
}

.service-detail-contact a {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
  transition: 0.3s;
  font-family: var(--primary-font), sans-serif;
}

.service-details-three h2 {
  color: #fff;
}

/*=== Brochures Box ===*/
.brochures-box .box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: #fff;
  transition: 0.3s;
}

.brochures-box .box:hover {
  background-color: #1a1e5d;
  color: #fff;
}

.brochures-box .box:hover h4,
.brochures-box .box:hover i {
  color: #fff;
}

.brochures-box .icon {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  flex: 0 0 60px;
  -ms-flex: 0 0 60px;
  max-width: 60px;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  background: #ff4328;
}

.brochures-box .icon i {
  font-size: 25px;
  color: #fff;
  transition: 0.3s;
}

.brochures-box .content {
  padding-inline-start: 20px;
}

/*Help Box*/
.help-box {
  position: relative;
  background-size: cover;
  padding: 30px 25px;
}

.help-box:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgba(54, 54, 54, 0.9);
}

.help-box > span {
  position: relative;
  display: block;
  font-size: 14px;
  line-height: 20px;
  color: #ffffff;
  margin-bottom: 10px;
}

.help-box h4 {
  position: relative;
  color: #ffffff;
  font-size: 21px;
  font-weight: 700;
  line-height: 1.2em;
  margin-bottom: 15px;
}

.help-box p {
  position: relative;
  color: #ffffff;
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 20px;
}

.help-box .theme-btn {
  padding: 8px 30px;
  font-size: 14px;
  font-weight: 600;
}

/***

====================================================================
		Service Detail
====================================================================

***/
.service-detail {
  position: relative;
  padding-inline-start: 20px;
}

.service-detail .images-box {
  position: relative;
  margin: 0 -5px;
}

.service-detail .images-box .column {
  padding: 0 5px;
}

.service-detail .images-box .image {
  position: relative;
  margin-bottom: 10px;
}

.service-detail .images-box .image img {
  display: block;
  width: 100%;
  height: auto;
}

.service-detail .content-box {
  position: relative;
  padding: 0px 0 0;
}

.service-detail .content-box h2 {
  position: relative;
  display: block;
  font-size: 32px;
  line-height: 1.2em;
  color: #101010;
  margin-bottom: 20px;
}

.service-detail .content-box h3 {
  position: relative;
  display: block;
  font-size: 24px;
  line-height: 1.2em;
  color: #101010;
  margin-bottom: 20px;
}

.service-detail .content-box p {
  position: relative;
  display: block;
  margin-bottom: 25px;
}

.service-detail .two-column {
  position: relative;
}

.service-detail .two-column .title {
  margin-bottom: 40px;
}

.service-detail .two-column p {
  margin-bottom: 25px;
}

.service-detail .two-column .image-column {
  position: relative;
  margin-bottom: 26px;
}

.service-detail .two-column .image-column .image {
  margin-bottom: 0;
}

.service-detail .two-column .text-column {
  position: relative;
}

.service-detail .prod-tabs {
  position: relative;
}

.service-detail .prod-tabs .tab-btns {
  position: relative;
  z-index: 1;
  border-bottom: 1px solid #dddddd;
}

.service-detail .prod-tabs .tab-btns .tab-btn {
  position: relative;
  top: 1px;
  display: block;
  float: left;
  margin-inline-end: 5px;
  font-size: 16px;
  color: #333333;
  background: #ffffff;
  text-transform: capitalize;
  font-weight: 400;
  line-height: 23px;
  cursor: pointer;
  border: 1px solid #e5e5e5;
  border-bottom: 0;
  padding: 10px 25px;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.service-detail .prod-tabs .tab-btns .tab-btn:before {
  position: absolute;
  content: "";
  top: 0;
  left: 50%;
  width: 0%;
  height: 3px;
  background-color: var(--primary-color);
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.service-detail .prod-tabs .tab-btns .tab-btn:hover:before,
.service-detail .prod-tabs .tab-btns .tab-btn.active-btn:before {
  width: 100%;
  left: 0;
}

.service-detail .prod-tabs .tab-btns .tab-btn:hover:after,
.service-detail .prod-tabs .tab-btns .tab-btn.active-btn:after {
  position: absolute;
  content: "";
  left: 0px;
  bottom: -3px;
  width: 100%;
  height: 5px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.service-detail .prod-tabs .tab-btns .tab-btn:hover,
.service-detail .prod-tabs .tab-btns .tab-btn.active-btn {
  color: var(--primary-color);
  background: #ffffff;
}

.service-detail .prod-tabs .tabs-content {
  position: relative;
  padding: 25px 30px 45px;
  border: 1px solid #dddddd;
}

.service-detail .prod-tabs .tabs-content .tab {
  position: relative;
  display: none;
  border-top: 0px;
}

.service-detail .prod-tabs .tabs-content .tab.active-tab {
  display: block;
}

.service-detail .prod-tabs .tabs-content .tab .content {
  position: relative;
}

.service-detail .prod-tabs .tabs-content .tab .content p {
  position: relative;
  font-size: 16px;
  line-height: 26px;
  color: #666666;
  margin-bottom: 0px;
}

.list-style-one {
  position: relative;
  display: block;
  margin-bottom: 30px;
}

.list-style-one li {
  position: relative;
  padding-inline-start: 25px;
  margin-bottom: 10px;
}

.list-style-one li::before {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
  line-height: 25px;
  color: var(--primary-color);
  content: "\f101";
  font-weight: 700;
  font-family: "Font Awesome 5 Pro";
}

/***

====================================================================
		Project Detail
====================================================================

***/
.project-detail {
  position: relative;
  padding: 100px 0 85px;
}

.project-detail .upper-box {
  position: relative;
  margin-bottom: 40px;
}

.project-detail .upper-box .image {
  position: relative;
  margin-bottom: 0;
}

.project-detail .upper-box .image img {
  display: block;
  width: 100%;
  height: auto;
}

.project-detail .single-item-carousel .owl-nav {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  margin-top: -50px;
}

.project-detail .single-item-carousel .owl-next,
.project-detail .single-item-carousel .owl-prev {
  position: absolute;
  left: 0;
  height: 100px;
  width: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  font-size: 18px;
  line-height: 100px;
  color: #ffffff;
  font-weight: 400;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.project-detail .single-item-carousel .owl-next {
  left: auto;
  inset-inline-end: 0px;
}

.project-detail .single-item-carousel .owl-next:hover,
.project-detail .single-item-carousel .owl-prev:hover {
  background-color: rgba(255, 255, 255, 0.5);
  color: #000000;
}

.project-detail .text-column {
  position: relative;
}

.project-detail .text-column .inner-column {
  position: relative;
  padding-inline-end: 30px;
}

.project-detail .text-column .inner-column .dropcap {
  float: left;
  background: var(--primary-color);
  border-radius: 20px;
  padding: 30px;
  color: #ffffff;
  font-size: 48px;
  font-weight: 600;
  margin-inline-end: 20px;
}

.project-detail .text-column .inner-column p strong {
  color: #101010;
}

.project-detail .pr-ul {
  margin-top: 10px;
  float: left;
  margin-bottom: 0px;
}

.project-detail .pr-ul li {
  display: flex;
  margin-bottom: 15px;
  float: left;
  width: 50%;
}

.project-detail .pr-ul .icon i {
  width: 40px;
  height: 40px;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 2px solid #eeeeee;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  margin-inline-end: 20px;
  color: var(--primary-color);
}

/* Shop */
.shop-area select {
  padding: 10px;
}

.product__content {
  padding-bottom: 30px;
  padding-inline-start: 30px;
  padding-inline-end: 30px;
}

.product__img {
  position: relative;
}

.product__img img {
  width: 100%;
}

.product:hover .product-action a {
  margin: 0 5px;
  opacity: 1;
  visibility: visible;
}

.product-action {
  position: absolute;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: 30px;
}

.product-action a {
  display: inline-block;
  background: #ffffff;
  line-height: 40px;
  color: #a39fb4;
  margin: 0 8px;
  opacity: 0;
  visibility: hidden;
  border-radius: 5px;
  padding: 0 20px;
}

.product-action a:hover {
  background: var(--primary-color);
  color: #ffffff;
}

.pro-cat {
  margin-bottom: 15px;
  display: block;
}

.pro-cat a {
  color: var(--primary-color);
  font-size: 14px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.pro-title {
  margin-bottom: 12px;
}

.pro-title a {
  font-size: 24px;
}

.pro-title a:hover {
  color: var(--primary-color);
}

.price span {
  color: #595b6b;
  font-size: 22px;
  display: inline-block;
  margin: 0 5px;
  font-weight: 700;
  font-family: var(--primary-font), sans-serif;
}

.price span.old-price {
  color: #b5b5b5;
  text-decoration: line-through;
}

.product-showing p {
  margin: 0;
  border: 2px solid #eaedff;
  padding: 17px 30px;
  text-align: center;
  line-height: 1;
  display: inline-block;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 2px;
  font-weight: 500;
}

@media (max-width: 767px) {
  .product-showing p {
    padding: 17px 15px;
  }
}
.pro-filter {
  position: relative;
  display: inline-block;
}

@media (max-width: 767px) {
  .pro-filter {
    float: left;
  }
}
.img,
img {
  max-width: 100%;
  transition: all 0.3s ease-out 0s;
}

.pro-filter select {
  border: 2px solid #eaedff;
  padding: 17px 30px;
  display: inline-block;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 2px;
  line-height: 1;
  color: #6f7172;
  appearance: none;
  -moz-appearance: none;
  width: 145px;
  font-weight: 500;
}

.pro-filter .nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-radius: 0;
  border: solid 1px #e8e8e8;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  float: left;
  font-family: inherit;
  font-size: 14px;
  font-weight: normal;
  height: 50px;
  line-height: 48px;
  outline: none;
  padding-inline-start: 18px;
  padding-inline-end: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: auto;
}

.pro-filter::before {
  content: "\f107";
  inset-inline-end: 15px;
  top: 15px;
  position: absolute;
  color: #758799;
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
}

.shop-tab ul li {
  margin-inline-start: 20px;
}

@media (max-width: 767px) {
  .shop-tab ul li {
    margin-inline-start: 10px;
  }
}
.shop-tab ul li a {
  font-size: 14px;
  font-weight: 500;
  color: #6f7172;
  letter-spacing: 2px;
  padding: 0;
  text-transform: uppercase;
  position: relative;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  background: #f6f6ff;
  line-height: 51px;
  text-align: center;
}

.shop-tab ul li a.active {
  color: white;
  background: #8fb569;
}

.shop-thumb-tab {
  width: 160px;
  float: right;
}

@media (max-width: 767px) {
  .shop-thumb-tab {
    float: none;
    width: 100%;
    margin-bottom: 0;
  }
}
@media (max-width: 767px) {
  .shop-thumb-tab ul {
    margin: 0 -5px;
  }
}
.shop-thumb-tab ul li {
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  .shop-thumb-tab ul li {
    width: 33.33%;
    float: left;
    padding: 0 5px;
  }
}
.shop-thumb-tab ul li a {
  padding: 0;
}

.product-details-img {
  margin-inline-end: 180px;
  overflow: hidden;
}

@media (max-width: 767px) {
  .product-details-img {
    margin-inline-start: 0;
  }
}
.product-details-title p {
  color: #6a667b;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.product-details-title h1 {
  font-size: 40px;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -2px;
  margin-bottom: 15px;
}

@media (max-width: 767px) {
  .product-details-title h1 {
    font-size: 36px;
  }
}
.details-price span {
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 400;
  margin-inline-start: 0;
  margin-inline-end: 10px;
}

.details-price {
  border-bottom: 2px solid #eaedff;
}

.product-cat span {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  color: #100d1c;
}

.product-cat a {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2px;
}

.product-cat a:hover {
  color: #8fb569;
}

.product-social a {
  margin-inline-end: 10px;
  background: #f5f5ff;
  height: 50px;
  width: 50px;
  line-height: 48px;
  border-radius: 30px;
  color: #6f7172;
  display: inline-block;
  text-align: center;
  font-size: 14px;
}

@media (max-width: 767px) {
  .product-social a {
    margin-bottom: 10px;
  }
}
.product-social a:hover {
  background: #8fb569;
  color: #ffffff;
}

.plus-minus {
  display: inline-block;
}

@media (max-width: 767px) {
  .plus-minus {
    display: block;
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .plus-minus {
    display: inline-block;
    margin-bottom: 0;
  }
}
.action-btn {
  background: #8fb569;
  padding: 20px 25px;
  border: none;
  margin-inline-start: 15px;
  color: #ffffff;
  display: inline-block;
}

.action-btn:hover {
  background: var(--primary-color);
  color: #ffffff;
}

.product-action-list {
  overflow: hidden;
}

.product-action-list a {
  float: left;
}

@media (max-width: 767px) {
  .product-action-list a.btn {
    padding: 23px 30px;
    margin-bottom: 7px;
  }
}
@media (max-width: 767px) {
  .product-action-list a.action-btn {
    margin-inline-start: 5px;
    margin-bottom: 7px;
  }
}
.cart-plus-minus input {
  height: 60px;
  width: 100px;
  border: 0;
  border: 2px solid #eaedff;
  text-align: center;
  -moz-appearance: none;
  appearance: none;
}

.cart-plus-minus {
  display: inline-block;
  position: relative;
  margin-inline-end: 15px;
}

.cart-plus-minus .qtybutton {
  position: absolute;
  top: 15px;
  inset-inline-start: 17px;
  font-size: 20px;
  color: #c4bedd;
  cursor: pointer;
}

.cart-plus-minus .inc {
  inset-inline-start: auto;
  inset-inline-end: 17px;
}

.additional-info .table td,
.additional-info .table th {
  padding: 0.75rem 0;
  vertical-align: top;
  border-top: 1px solid #e4e4e4;
  font-weight: 400;
}

.shop-cat a {
  padding: 45px 30px;
  border: 2px solid #eaedff;
  display: block;
}

.shop-cat a i {
  font-size: 100px;
  color: #8fb569;
  margin-bottom: 30px;
  transition: 0.3s;
}

.shop-cat a h4 {
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  transition: 0.3s;
  letter-spacing: 1px;
}

.shop-cat a:hover {
  background: #8fb569;
  border-color: #8fb569;
}

.shop-cat a:hover i {
  color: #ffffff;
}

.shop-cat a:hover h4 {
  color: #ffffff;
}

.product-list-content .price span:first-child {
  margin-inline-start: 0;
}

.bakix-details-tab ul {
  border-bottom: 2px solid #e1e1e1;
}

.bakix-details-tab ul li a {
  font-size: 16px;
  font-weight: 500;
  color: #101010;
  letter-spacing: 2px;
  padding: 0;
  text-transform: capitalize;
  position: relative;
  padding: 0 25px;
}

@media (max-width: 767px) {
  .bakix-details-tab ul li a {
    padding: 0 10px;
  }
}
.bakix-details-tab ul li a.active {
  color: var(--primary-color);
}

.bakix-details-tab ul li a.active:before {
  position: absolute;
  bottom: -32px;
  inset-inline-start: 0;
  height: 2px;
  width: 100%;
  content: "";
  background: var(--primary-color);
  transition: 0.3s;
}

@media (max-width: 767px) {
  .bakix-details-tab ul li a.active:before {
    display: none;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .bakix-details-tab ul li a.active:before {
    display: block;
  }
}
/* Cart */
.table-content table {
  background: #ffffff;
  border-color: #eaedff;
  border-radius: 0;
  border-style: solid;
  border-width: 1px 0 0 1px;
  text-align: center;
  width: 100%;
  margin-bottom: 0;
}

.table-content table td.product-name {
  font-size: 16px;
  font-weight: 400;
  text-transform: capitalize;
}

.table-content table td.product-name a:hover {
  color: var(--primary-color);
}

.table-content table td {
  border-top: medium none;
  padding: 20px 10px;
  vertical-align: middle;
  font-size: 16px;
}

.table-content table th,
.table-content table td {
  border-bottom: 1px solid #eaedff;
  border-inline-end: 1px solid #eaedff;
}

.product-quantity input {
  border: none;
  color: #6f7172;
  font-size: 14px;
  font-weight: normal;
  border: 0;
}

.table td,
.table th {
  border-top: 1px solid #eaedff;
}

.product-quantity > input {
  width: 80px;
  border-radius: 3px;
}

.table-content table td.product-subtotal {
  font-size: 16px;
}

.table-content table td .cart-plus-minus {
  float: none;
  margin: 0 auto;
}

.coupon-all {
  margin-top: 50px;
}

.coupon {
  float: left;
}

@media (max-width: 767px) {
  .coupon {
    float: none;
  }
}
#coupon_code {
  height: 62px;
  border: 2px solid #eaedff;
  padding: 0 15px;
  margin-inline-end: 10px;
}

@media (max-width: 767px) {
  #coupon_code {
    margin-bottom: 15px;
  }
}
.coupon2 {
  float: right;
}

@media (max-width: 767px) {
  .coupon2 {
    float: none;
    margin-top: 15px;
  }
}
.cart-page-total {
  padding-top: 50px;
}

.cart-page-total > h2 {
  font-size: 25px;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.cart-page-total > ul {
  border: 1px solid #eaedff;
}

.cart-page-total > ul > li {
  list-style: none;
  font-size: 15px;
  color: #6f7172;
  padding: 10px 30px;
  border-bottom: 1px solid #eaedff;
  font-weight: 400;
}

.cart-page-total ul > li > span {
  float: right;
}

.cart-page-total li:last-child {
  border-bottom: 0;
}

td.product-thumbnail img {
  width: 125px;
}

/* 12. project */
.single-project .project-info {
  position: absolute;
  bottom: 40px;
  inset-inline-start: 40px;
  color: #fff;
}

.single-project .project-info h4 {
  margin-bottom: 0;
}

.single-project .project-info h4 a {
  color: #fff;
  font-size: 24px;
}

.single-project .project-info p {
  color: var(--primary-color);
  margin-bottom: 0;
}

.single-project .project-info a {
  color: var(--primary-color);
}

.single-project .project-info a img {
  display: inline;
  margin-inline-start: 5px;
}

.portfolio-active {
  padding-inline-start: 40px;
}

.portfolio-area2 h2 {
  color: #fff;
}

.portfolio-active .single-project {
  position: relative;
}

.portfolio-active .slick-arrow {
  position: absolute;
  top: -124px;
  inset-inline-end: 19%;
  border: none;
  background: none;
  padding: 0;
  color: #fff;
  z-index: 9;
  cursor: pointer;
  transition: 0.3s;
  width: 60px;
  height: 60px;
  text-align: center;
  border-radius: 50%;
  color: var(--primary-color);
  box-shadow: 1px 1.732px 30px 0px rgba(255, 94, 21, 0.25);
  font-size: 30px;
}

.portfolio-active .slick-arrow:hover {
  background: var(--primary-color);
  color: #fff;
}

.portfolio-active .slick-next {
  inset-inline-end: 15%;
  inset-inline-start: inherit;
}

.portfolio-box {
  width: 100%;
  height: 390px;
  box-shadow: 2.5px 4.33px 15px 0px rgba(0, 0, 0, 0.07);
  overflow: hidden;
  border-radius: 10px;
  position: relative;
}

.portfolio-box img {
  height: 390px !important;
  max-width: 600px !important;
  margin-inline-start: -150px;
}

.portfolio-box .caption {
  position: absolute;
  bottom: -150px;
  padding: 30px;
  width: 100%;
  background: #fff;
  transition: opacity 0.4s ease, visibility 0.2s ease;
  opacity: 0;
}

.portfolio-box:hover .caption {
  bottom: 0;
  opacity: 1;
}

.portfolio-box .caption .caption-text {
  position: relative;
}

.portfolio-box .caption .arrow-icon {
  position: absolute;
  inset-inline-end: 40px;
  top: 25%;
  font-size: 28px;
  color: var(--primary-color);
}

.caption-text h5 {
  margin-bottom: 5px !important;
}

.caption-text p {
  margin-bottom: 0 !important;
}

.project-two h2 {
  color: #fff;
}

.project-two .grid-item,
.project-two .col-xl-6 {
  padding: 0;
  margin: 0;
}

/* Lower Content */
.project-detail .lower-content {
  position: relative;
}

.project-detail .lower-content h2 {
  position: relative;
  display: block;
  margin-bottom: 20px;
}

.project-detail .lower-content h3 {
  position: relative;
  display: block;
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.project-detail .lower-content h4 {
  position: relative;
  display: block;
  font-size: 22px;
  margin-bottom: 20px;
}

.project-detail .lower-content p {
  position: relative;
  display: block;
  margin-bottom: 30px;
}

.project-detail .info-column {
  position: relative;
}

.project-detail .info-column .inner-column {
  position: relative;
  padding: 30px;
  background: #fff;
  border: 2px solid #f5f5f5 !important;
}

.project-detail .project-info {
  position: relative;
}

.project-detail .project-info li {
  position: relative;
  margin-bottom: 15px;
  float: left;
  width: 100%;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 15px;
}

.project-detail .project-info h5 {
  float: left;
}

.project-detail .project-info span {
  float: right;
}

.project-detail .project-info li:last-child {
  padding-bottom: 0;
  border-bottom: none;
  margin-bottom: 0;
}

.project-detail .project-info li .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 18px;
  line-height: 28px;
  color: var(--primary-color);
}

.project-detail .project-info li strong {
  color: #101010;
  font-size: 20px;
  line-height: 24px;
  display: inline-block;
  font-weight: 600;
}

.project-detail .project-info li p {
  font-size: 15px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

.project-detail .project-info li p a {
  display: inline-block;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.project-detail .project-info li p a:hover {
  color: var(--primary-color);
}

.project-detail .lower-content th {
  background: var(--primary-color);
  color: #fff;
}

/*# sourceMappingURL=theme.css.map */
