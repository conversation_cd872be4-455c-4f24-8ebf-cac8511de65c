name: Add new issues and pr to project

on:
  pull_request:
    types:
      - opened
  issues:
    types:
      - opened

jobs:
  add-to-project:
    name: Add new issue and pr to project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/add-to-project@v1.0.2
        with:
          project-url: https://github.com/orgs/tabler/projects/9/views/1
          github-token: ${{ secrets.GITHUB_TOKEN }}
