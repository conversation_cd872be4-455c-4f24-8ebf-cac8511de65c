.contact-form * {
  box-sizing: border-box;
}
.contact-form .button-loading {
  border: 1px solid #c4cdd5;
  cursor: default;
  text-shadow: none;
  color: transparent !important;
  position: relative;
  transition: border-color 0.2s ease-out;
}
.contact-form .button-loading:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  border-width: 3px;
  border-style: solid;
  margin-top: -9px;
  margin-left: -9px;
  width: 18px;
  height: 18px;
  animation: button-loading-spinner 1s linear infinite;
  border-color: #ffffff;
  border-bottom-color: transparent;
}
.contact-form .button-loading:hover, .contact-form .button-loading:focus, .contact-form .button-loading:active {
  color: transparent;
}
.contact-form .contact-message {
  margin: 0 0 20px 0;
  padding: 15px 30px 15px 15px;
  border-left: 5px solid #eeeeee;
  border-radius: 0;
  display: none;
  width: 100%;
}
.contact-form .contact-message.contact-success-message {
  background-color: #c0edf1;
  border-color: #58d0da;
  color: #000;
}
.contact-form .contact-message.contact-error-message {
  background-color: #faeaa9;
  border-color: #f3cc31;
  color: #000;
}
.contact-form .contact-form-row {
  margin-right: -15px;
  margin-left: -15px;
}
.contact-form .contact-form-row:after, .contact-form .contact-form-row:before {
  box-sizing: border-box;
  display: table;
  content: " ";
}
.contact-form .contact-form-row:after {
  clear: both;
}
.contact-form .contact-form-group {
  margin-bottom: 15px;
}
.contact-form .contact-column-6,
.contact-form .contact-column-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.contact-form .contact-form-input-group {
  margin-bottom: 15px;
}
.contact-form .contact-label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: 700;
}
.contact-form .contact-label.required:after {
  content: " *";
  color: red;
}
.contact-form .contact-form-input {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555555;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.contact-form .form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.contact-form .form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #fff;
  background-image: var(--bs-form-check-bg-image);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid #dee2e6;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  padding: 0;
}
.contact-form .form-check .form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.contact-form .form-check .form-check-input[type=radio] {
  border-radius: 50%;
}
.contact-form .form-check .form-check-input:active {
  filter: brightness(90%);
}
.contact-form .form-check .form-check-input:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.contact-form .form-check .form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.contact-form .form-check .form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e") ;
}
.contact-form .form-check .form-check-input:checked[type=radio] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e") ;
}
.contact-form .form-check .form-check-input[type=checkbox]:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e") ;
}
.contact-form .form-check .form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.contact-form .form-check .form-check-input:disabled ~ .form-check-label, .contact-form .form-check .form-check-input[disabled] ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}
.contact-form .contact-form-input:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.contact-form .contact-form-input:focus::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
.contact-form .contact-form-input:focus:-ms-input-placeholder {
  color: #999999;
}
.contact-form .contact-form-input:focus::-webkit-input-placeholder {
  color: #999999;
}
.contact-form .contact-form-input:focus::-ms-expand {
  background-color: transparent;
  border: 0;
}
.contact-form textarea.contact-form-input {
  height: auto;
}
.contact-form .contact-button {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid #2e6da4;
  border-radius: 4px;
  color: #ffffff;
  background-color: #337ab7;
}
.contact-form .contact-button:hover, .contact-form .contact-button:focus {
  color: #ffffff;
  background-color: #286090;
  border-color: #204d74;
}
@media (min-width: 992px) {
  .contact-form .contact-column-6 {
    width: 50%;
    float: left;
  }
  .contact-form .contact-column-12 {
    width: 100%;
  }
}
@keyframes button-loading-spinner {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
