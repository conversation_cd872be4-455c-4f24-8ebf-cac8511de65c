.swatches-container .header {
  display: flex;
  flex-direction: row;
  font-weight: 700;
  background-color: var(--bb-body-bg);
  color: var(--bb-body-color);
  border-bottom: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
}
.swatches-container .header > * {
  float: left;
  padding: 10px;
  text-align: center;
}
.swatches-container .swatches-list {
  float: left;
  list-style: none;
  margin: 0 0 15px;
  padding: 0;
  width: 100%;
}
.swatches-container .swatches-list li {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}
.swatches-container .swatches-list li + li {
  margin-top: 1px;
}
.swatches-container .swatches-list li:nth-child(odd) {
  background-color: var(--bb-body-bg);
}
.swatches-container .swatches-list li > * {
  float: left;
  padding: 10px;
  text-align: center;
}
.swatches-container .swatches-list li .image-box-container {
  width: 34px;
  margin: 0 auto;
  font-size: 0;
}
.swatches-container .swatches-list li .image-box-container img {
  border: 1px solid #cccccc;
  width: 34px;
  height: 34px;
}
.swatches-container .swatch-item {
  flex: 1;
  max-width: 90px;
}
.swatches-container .swatch-is-default {
  width: 120px;
}
.swatches-container .remove-item {
  width: 80px;
}
.swatches-container .swatch-exchange-rate,
.swatches-container .swatch-is-prefix-symbol,
.swatches-container .swatch-decimals {
  max-width: 180px;
}
