@charset "UTF-8";
.dd-list .dd-item > button {
  position: absolute;
  top: 0.5rem;
  inset-inline-end: 0.55rem;
  font-size: 1rem;
}
.dd-list .dd-placeholder {
  padding: 0;
  min-height: 30px;
  background: rgba(var(--bb-info-rgb), 0.1);
  border: var(--bb-card-border-width) dashed var(--bb-border-color);
  box-sizing: border-box;
  display: block;
  position: relative;
  margin: 0;
  font-size: 13px;
  line-height: 20px;
}
.dd-list .dd3-handle {
  top: 5px;
  height: 40px;
  background: var(--bb-body-bg);
  border: var(--bb-list-group-border-width) solid var(--bb-list-group-border-color);
  position: absolute;
  margin: 0;
  left: 0;
  cursor: move;
  width: 30px;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.dd-list .dd3-handle:before {
  top: 8px;
  color: var(--bb-body-color);
  content: "≡";
  display: block;
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
  text-indent: 0;
  font-size: 20px;
  font-weight: normal;
}
.dd-list .dd3-content {
  height: 40px;
  border: var(--bb-list-group-border-width) solid var(--bb-list-group-border-color);
  background: var(--bb-bg-surface);
  color: inherit;
  display: block;
  margin: 5px 0;
  padding: 5px 10px 5px 40px;
  text-decoration: none;
  font-weight: 400;
  border-radius: 3px;
  box-sizing: border-box;
}
.dd-list .dd3-content .badge {
  margin-inline-start: 0.25rem;
}
.dd-list .dd3-content.active .fetch-data {
  font-weight: 700;
}
.dd-list .dd3-content.active .delete-button {
  display: flex;
}
.dd-list .dd3-content .delete-button {
  display: none;
}

.dd .dd-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bb-body-color);
}
