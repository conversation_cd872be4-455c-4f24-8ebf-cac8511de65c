.nav-segmented {
  --#{$prefix}nav-bg: var(--#{$prefix}bg-surface-tertiary);
  --#{$prefix}nav-padding: 2px;
  --#{$prefix}nav-height: 2.5rem;
  --#{$prefix}nav-gap: .25rem;
  --#{$prefix}nav-active-bg: var(--#{$prefix}bg-surface);
  --#{$prefix}nav-font-size: inherit;
  --#{$prefix}nav-radius: 6px;


  --#{$prefix}nav-link-disabled-color: var(--#{$prefix}disabled-color);
  --#{$prefix}nav-link-gap: .25rem;
  --#{$prefix}nav-link-padding-x: .75rem;
  --#{$prefix}nav-link-icon-size: 1.25rem;
  display: inline-flex;
  flex-wrap: wrap;
  gap: var(--#{$prefix}nav-gap);
  padding: var(--#{$prefix}nav-padding);
  list-style: none;
  background: var(--#{$prefix}nav-bg);
  border-radius: calc(var(--#{$prefix}nav-radius) + var(--#{$prefix}nav-padding));
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .04);

  .nav-link {
    display: inline-flex;
    gap: calc(.25rem + var(--#{$prefix}nav-link-gap));
    align-items: center;
    margin: 0;
    font-size: var(--#{$prefix}nav-font-size);
    min-width: calc(var(--#{$prefix}nav-height) - 2 * var(--#{$prefix}nav-padding)); 
    height: calc(var(--#{$prefix}nav-height) - 2 * var(--#{$prefix}nav-padding));
    padding: 0 calc(var(--#{$prefix}nav-link-padding-x) - 2px);
    border: 1px solid transparent;
    background: transparent;
    color: var(--#{$prefix}secondary);
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    cursor: pointer;
    transition: background-color $transition-time, color $transition-time;
    border-radius: var(--#{$prefix}nav-radius);
    flex-grow: 1;   
    justify-content: center;

    &:hover,
    &.hover  {
      background: rgba(0, 0, 0, .04);
      color: var(--#{$prefix}body-color);
    }

    &.disabled,
    &:disabled {
      color: var(--#{$prefix}nav-link-disabled-color);
      cursor: not-allowed;
    }
  }

  .nav-link-input:checked + .nav-link,
  .nav-link.active {
    color: var(--#{$prefix}body-color);
    background: var(--#{$prefix}nav-active-bg);
    border-color: var(--#{$prefix}border-color);
  }

  .nav-link-input {
    display: none;
  }

  .nav-link-icon {
    width: var(--#{$prefix}nav-link-icon-size);
    height: var(--#{$prefix}nav-link-icon-size);
    margin: 0 -.25rem;
    color: inherit;
  }
}

.nav-segmented-vertical {
  flex-direction: column;

  .nav-link {
    justify-content: flex-start;
  }
}

.nav-sm {
  --#{$prefix}nav-height: 2rem;
  --#{$prefix}nav-font-size: var(--tblr-font-size-h5);
  --#{$prefix}nav-radius: 4px;
  --#{$prefix}nav-link-padding-x: .5rem;
  --#{$prefix}nav-link-gap: .25rem;
  --#{$prefix}nav-link-icon-size: 1rem;
}

.nav-lg {
  --#{$prefix}nav-height: 3rem;
  --#{$prefix}nav-font-size: var(--tblr-font-size-h3);
  --#{$prefix}nav-radius: 8px;
  --#{$prefix}nav-link-padding-x: 1rem;
  --#{$prefix}nav-link-gap: .5rem;
  --#{$prefix}nav-link-icon-size: 1.5rem;
}