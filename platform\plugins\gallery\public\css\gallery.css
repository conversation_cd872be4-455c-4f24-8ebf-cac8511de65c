#list-photo {
  width: 100%;
  margin: 0 -7px;
}
#list-photo .item {
  width: 33.333%;
  margin-bottom: 14px;
}
#list-photo .item .photo-item {
  padding-left: 7px;
  padding-right: 7px;
}
#list-photo .item .photo-item div {
  transition: all 0.25s;
  padding: 5px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.3);
}
#list-photo .item .photo-item div:hover {
  background: rgba(63, 63, 62, 0.1);
}
#list-photo .item .photo-item img {
  border: 1px solid rgba(63, 63, 62, 0.4);
  display: block;
  max-width: 100%;
}

.lg-outer .lg-thumb {
  margin: 0 auto !important;
}

.lg-sub-html {
  bottom: 100px !important;
}
.lg-sub-html.inactive {
  bottom: 0 !important;
}

.gallery-wrap .gallery-item {
  width: 32.8%;
  margin-right: 0.8%;
  float: left;
  max-height: 250px;
  overflow: hidden;
  margin-bottom: 10px;
  position: relative;
}
.gallery-wrap .gallery-item:nth-child(3n) {
  margin-right: 0;
}
.gallery-wrap .gallery-item .gallery-detail {
  position: absolute;
  bottom: -50px;
  right: 0;
  left: 0;
  z-index: 2;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 4px 10px;
  transition: ease 0.25s;
}
.gallery-wrap .gallery-item .gallery-detail a {
  color: #fff;
}
.gallery-wrap .gallery-item .gallery-detail a:hover {
  color: #32c5d2 !important;
}
.gallery-wrap .gallery-item .gallery-detail .gallery-title {
  text-transform: uppercase;
  font-weight: bold;
}
.gallery-wrap .gallery-item:hover .gallery-detail {
  bottom: 0;
}
.gallery-wrap .gallery-item .img-wrap {
  overflow: hidden;
}
.gallery-wrap .gallery-item .img-wrap img {
  width: 100%;
}

@media screen and (max-width: 767px) {
  .gallery-wrap .gallery-item {
    width: 100%;
  }
  .gallery-wrap .gallery-item .gallery-detail {
    bottom: 0;
  }
}
body[dir=rtl] .lg-outer {
  direction: ltr;
}
