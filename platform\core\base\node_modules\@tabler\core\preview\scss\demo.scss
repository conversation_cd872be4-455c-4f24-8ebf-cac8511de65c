$prefix: "tblr-";

@import "highlight";

.dropdown-menu-demo {
  display: inline-block;
  width: 100%;
  position: relative;
  top: 0;
  margin-bottom: 1rem !important;
}

.demo-icon-preview {
  position: sticky;
  top: 0;

  svg,
  i {
    width: 15rem;
    height: 15rem;
    font-size: 15rem;
    stroke-width: 1.5;
    margin: 0 auto;
    display: block;

    // @include media-breakpoint-down(sm) {
    //   width: 10rem;
    //   height: 10rem;
    //   font-size: 10rem;
    // }
  }
}

.demo-icon-preview-icon {
  pre {
    margin: 0;
    user-select: all;
  }
}

.demo-dividers {
  > p {
    opacity: 0.2;
    user-select: none;
  }
}

$demo-icon-size: 4rem;
.demo-icons-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 -2px -1px 0;
  list-style: none;

  > * {
    flex: 1 0 $demo-icon-size;
  }
}

.demo-icons-list-wrap {
  overflow: hidden;
}

.demo-icons-list-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
  text-align: center;
  padding: 0.5rem;
  border-right: var(--#{$prefix}border-width) var(--#{$prefix}border-style)
    var(--#{$prefix}border-color);
  border-bottom: var(--#{$prefix}border-width) var(--#{$prefix}border-style)
    var(--#{$prefix}border-color);
  color: inherit;
  cursor: pointer;

  .icon {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1.5rem;
  }

  &:hover {
    text-decoration: none;
  }
}